#pragma once
#include "DMAMemoryManagement/includes.h"
#include "InventoryItem.h"
#include "CargoGrid.h"

namespace DayZ {
	struct EntityInventory : public DMAMem::MemoryObject {
		std::shared_ptr<DayZ::InventoryItem> handItem;
		std::shared_ptr<DayZ::CargoGrid> cargoGrid;
		std::shared_ptr<DayZ::CargoGrid> playerCargoGrid; // For player inventories (0x150)
		bool isHandItemValid;

		EntityInventory() {
			handItem = std::shared_ptr<DayZ::InventoryItem>(new DayZ::InventoryItem());
			cargoGrid = std::shared_ptr<DayZ::CargoGrid>(new DayZ::CargoGrid());
			playerCargoGrid = std::shared_ptr<DayZ::CargoGrid>(new DayZ::CargoGrid());

			// Hand item at 0x1B0 (working offset)
			this->registerPointer(0x1B0, handItem.get());
			this->registerOffset(0x1CC, &isHandItemValid, sizeof(bool));

			// Forum offsets:
			// Item inventory cargo grid at 0x148 (ItemInventory::CargoGrid -> 0x148)
			this->registerPointer(0x148, cargoGrid.get());
			// Player inventory cargo grid at 0x150 (PlayerInventory::CargoGrid -> 0x150)
			this->registerPointer(0x150, playerCargoGrid.get());
		}

		// Get the appropriate cargo grid based on entity type
		std::shared_ptr<DayZ::CargoGrid> getCargoGrid(bool isPlayer = false) {
			return isPlayer ? playerCargoGrid : cargoGrid;
		}
	};
}