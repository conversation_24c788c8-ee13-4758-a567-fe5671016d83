#include "EntityPool.h"
#include "Entity.h"
#include <vector>
#include <memory>

namespace DayZ {

EntityPtr EntityPool::acquire() {
    return std::make_shared<Entity>();
}

void EntityPool::release(EntityPtr entity) {
    // Reset entity state
    entity->id = 0;
    entity->name.clear();
    entity->position = DMARender::Vector3();
    entity->rotation = 0.0f;
    entity->isAlive = false;
    entity->isPlayer = false;
    entity->isZombie = false;
    entity->isAnimal = false;
    entity->isVehicle = false;
    entity->isLoot = false;
    entity->_entityType = ENTITY_TYPE::NONE;
    entity->_isEntityTypeChecked = false;
    entity->_isValid = false;
    entity->_isValidChecked = false;
}

void EntityPool::clear() {
    // Nothing to do here since we're using make_shared
}

} // namespace DayZ 