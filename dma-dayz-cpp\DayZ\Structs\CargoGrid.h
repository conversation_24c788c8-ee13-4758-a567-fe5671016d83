#pragma once
#include "DMAMemoryManagement/includes.h"
#include "InventoryItem.h"
#include <vector>
#include <memory>

namespace DayZ {
    
    // CargoGrid structure for container contents
    // Based on forum info: Updated with working offsets from forum post
    class CargoGrid : public DMAMem::MemoryObject {
    private:
        bool _isResolved = false;
        QWORD _lastItemListPtr = 0;
        int32_t _lastItemCount = 0;

    public:
        // Pointer to the item list array
        QWORD ItemListPtr;
        int32_t ItemCount;

        // Resolved items from the cargo
        std::vector<std::shared_ptr<InventoryItem>> CargoItems;

        CargoGrid() {
            // Forum working offsets:
            // CargoGrid + 0x38 = cargobase (pointer to item array)
            // CargoGrid + 0x44 = count (number of items)
            this->registerOffset(0x38, &ItemListPtr, sizeof(QWORD));
            this->registerOffset(0x44, &ItemCount, sizeof(int32_t));
        }

        // Resolve individual cargo items using forum method
        void resolveCargoItems(DMAMem::VmmManager* vmm, DWORD pid) {
            if (ItemListPtr == 0 || ItemCount <= 0 || ItemCount > 300) {
                CargoItems.clear();
                return;
            }

            // Check if we need to refresh (item list changed)
            bool needsRefresh = (_lastItemListPtr != ItemListPtr || _lastItemCount != ItemCount || CargoItems.empty());

            if (!needsRefresh) {
                return; // Use cached data
            }

            // Clear old data and update cache markers
            CargoItems.clear();
            CargoItems.reserve(ItemCount);
            _lastItemListPtr = ItemListPtr;
            _lastItemCount = ItemCount;

            // Forum method: Read items from cargobase + 0x8 + (i * 0x10)
            for (uint32_t i = 0; i < (uint32_t)ItemCount; i++) {
                QWORD itemPtr = 0;
                if (!vmm->readMemory(pid, ItemListPtr + 0x8 + (i * 0x10), &itemPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
                    continue;
                }

                if (itemPtr != 0 && itemPtr > 0x10000 && itemPtr < 0x7FFFFFFFFFFF) { // Basic pointer validation
                    auto cargoItem = std::make_shared<InventoryItem>();
                    try {
                        cargoItem->resolveObject(vmm, pid, itemPtr);

                        if (cargoItem->isValid()) {
                            CargoItems.push_back(cargoItem);
                            // Removed recursive nested detection for performance
                        }
                    }
                    catch (...) {
                        // Skip invalid items that cause exceptions
                        continue;
                    }
                }
            }
        }

        // Force refresh of cargo items (call when you know items have changed)
        void forceRefresh() {
            _lastItemListPtr = 0;
            _lastItemCount = 0;
            CargoItems.clear();
        }


        
        // Get CleanName for item (display name) - DayZ2 style
        std::string getItemCleanName(const std::shared_ptr<InventoryItem>& item, DMAMem::VmmManager* vmm, DWORD pid) const {
            if (!item || !item->EntityTypePtr) return "";

            try {
                // Get clean name pointer (EntityType + 0x4F0) - Display name
                QWORD cleanNamePtr = 0;
                if (!vmm->readMemory(pid, item->EntityTypePtr->_remoteAddress + 0x4F0, &cleanNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
                    return "";
                }
                if (cleanNamePtr == 0) return "";

                // Read the ArmaString directly
                return readArmaStringDirect(vmm, pid, cleanNamePtr);
            }
            catch (...) {
                return "";
            }
        }

        // Get ConfigName for item (category name) - DayZ2 style
        std::string getItemConfigName(const std::shared_ptr<InventoryItem>& item, DMAMem::VmmManager* vmm, DWORD pid) const {
            if (!item || !item->EntityTypePtr) return "";

            try {
                // Get config name pointer (EntityType + 0xA8) - Category name
                QWORD configNamePtr = 0;
                if (!vmm->readMemory(pid, item->EntityTypePtr->_remoteAddress + 0xA8, &configNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
                    return "";
                }
                if (configNamePtr == 0) return "";

                return readArmaStringDirect(vmm, pid, configNamePtr);
            }
            catch (...) {
                return "";
            }
        }

        // Helper function to read ArmaString directly
        std::string readArmaStringDirect(DMAMem::VmmManager* vmm, DWORD pid, QWORD stringPtr) const {
            try {
                // Read string length
                int32_t length = 0;
                if (!vmm->readMemory(pid, stringPtr + 0x8, &length, sizeof(int32_t), VMMDLL_FLAG_NOCACHE)) {
                    return "";
                }

                if (length <= 0 || length > 200) return ""; // Sanity check

                // Read string data
                std::vector<char> buffer(length + 1, 0);
                if (!vmm->readMemory(pid, stringPtr + 0x10, buffer.data(), length, VMMDLL_FLAG_NOCACHE)) {
                    return "";
                }

                return std::string(buffer.data());
            }
            catch (...) {
                return "";
            }
        }

        // Validate if a string is a good item name
        bool isValidItemName(const std::string& name) const {
            return name.length() > 0 && name.length() < 50 &&
                   name.find("\\") == std::string::npos &&
                   name.find("/") == std::string::npos &&
                   name.find(".p3d") == std::string::npos &&
                   name.find(".cfg") == std::string::npos &&
                   name.find("$UNT$") == std::string::npos &&
                   name.find('\0') == std::string::npos;
        }

        // Get valid cargo items
        const std::vector<std::shared_ptr<InventoryItem>>& getCargoItems() const {
            return CargoItems;
        }
        
        // Check if cargo has items
        bool hasItems() const {
            return !CargoItems.empty();
        }
        
        // Get cargo item count
        size_t getCargoItemCount() const {
            return CargoItems.size();
        }
    };
}
