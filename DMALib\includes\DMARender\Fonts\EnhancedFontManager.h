#pragma once
#include "imgui.h"
#include "EmbeddedFonts.h"
#include <string>
#include <unordered_map>

namespace DMARender {

// DayZ2-style enhanced font manager with high-quality rendering and text stroke
class EnhancedFontManager {
public:
    // Font types for different ESP elements
    enum class FontType {
        PLAYER_NAME,     // Player names (medium size)
        PLAYER_DISTANCE, // Distance text (small size)
        PLAYER_HAND,     // Hand items (small size)
        LOOT_NEAR,       // Nearby loot (medium size)
        LOOT_FAR,        // Distant loot (small size)
        ZOMBIE_TEXT,     // Zombie names (medium size)
        ANIMAL_TEXT,     // Animal names (medium size)
        UI_TEXT,         // Menu/UI text (medium size)
        DEBUG_TEXT       // Debug information (small size)
    };

    // Text stroke configuration
    struct StrokeConfig {
        bool enabled = true;
        ImU32 strokeColor = IM_COL32(0, 0, 0, 255); // Black outline
        float strokeWidth = 1.0f;
        int strokeDirections = 4; // 4-direction or 8-direction stroke
    };

private:
    // Font storage
    std::unordered_map<FontType, ImFont*> fonts;
    
    // Default fonts (DayZ2 dual system)
    ImFont* tahomaFont = nullptr;   // System fallback font
    ImFont* cousineFont = nullptr;  // Embedded primary font
    
    // Font configurations
    static constexpr float SMALL_FONT_SIZE = 14.0f;   // DayZ2 Tahoma size
    static constexpr float MEDIUM_FONT_SIZE = 15.0f;  // DayZ2 Cousine size
    static constexpr float LARGE_FONT_SIZE = 16.0f;   // Slightly larger for important text
    
    // High-quality font config (DayZ2 style)
    EmbeddedFonts::HighQualityFontConfig fontConfig;

public:
    EnhancedFontManager() = default;
    ~EnhancedFontManager() = default;

    // Initialize the font system (call during ImGui setup)
    bool initialize();
    
    // Get font for specific ESP element
    ImFont* getFont(FontType type) const;
    
    // DayZ2-style text drawing with stroke/outline
    void drawText(ImDrawList* drawList, const std::string& text, ImVec2 position, 
                  ImU32 color, FontType fontType = FontType::UI_TEXT, 
                  const StrokeConfig& stroke = StrokeConfig{});
    
    // DayZ2-style text drawing with custom font size
    void drawTextWithSize(ImDrawList* drawList, const std::string& text, ImVec2 position,
                         float fontSize, ImU32 color, 
                         const StrokeConfig& stroke = StrokeConfig{});
    
    // DayZ2-style centered text drawing
    void drawCenteredText(ImDrawList* drawList, const std::string& text, ImVec2 position,
                         ImU32 color, FontType fontType = FontType::UI_TEXT,
                         const StrokeConfig& stroke = StrokeConfig{});
    
    // Calculate text size for positioning
    ImVec2 calculateTextSize(const std::string& text, FontType fontType) const;
    ImVec2 calculateTextSizeWithFont(const std::string& text, float fontSize) const;
    
    // Font management
    void reloadFonts();
    bool isInitialized() const { return cousineFont != nullptr || tahomaFont != nullptr; }

private:
    // Internal font loading methods
    bool loadSystemFonts();
    bool loadEmbeddedFonts();
    void setupFontMappings();
    
    // DayZ2-style stroke rendering (4-direction)
    void drawTextStroke4Direction(ImDrawList* drawList, ImFont* font, float fontSize,
                                 ImVec2 position, const std::string& text, 
                                 const StrokeConfig& stroke);
    
    // Enhanced stroke rendering (8-direction)
    void drawTextStroke8Direction(ImDrawList* drawList, ImFont* font, float fontSize,
                                 ImVec2 position, const std::string& text,
                                 const StrokeConfig& stroke);
};

} // namespace DMARender
