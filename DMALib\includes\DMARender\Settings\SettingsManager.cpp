#include "SettingsManager.h"
#include "../RenderWindow/RenderBridge/RenderBridge.h"
#include "../../../dma-dayz-cpp/DayZ/Config/MultiCoreConfig.h"
#include <sstream>

namespace DMARender {

    SettingsManager::SettingsManager(const std::string& filePath) 
        : settingsFilePath(filePath) {
        loadSettings();
    }

    bool SettingsManager::saveSettings() {
        fflush(stdout);
        try {
            std::ofstream file(settingsFilePath);
            if (!file.is_open()) {
                std::cerr << "Failed to open settings file for writing: " << settingsFilePath << std::endl;
                return false;
            }

            file << "[Radar]" << std::endl;
            file << "showDeadPlayers=" << (settings.showDeadPlayers ? "1" : "0") << std::endl;
            file << "showVehicles=" << (settings.showVehicles ? "1" : "0") << std::endl;
            file << "showGrounditems=" << (settings.showGrounditems ? "1" : "0") << std::endl;
            file << "showBoats=" << (settings.showBoats ? "1" : "0") << std::endl;
            file << "showDeadAnimals=" << (settings.showDeadAnimals ? "1" : "0") << std::endl;
            file << "showClothing=" << (settings.showClothing ? "1" : "0") << std::endl;
            file << "showWeapons=" << (settings.showWeapons ? "1" : "0") << std::endl;
            file << "showProxyMagazines=" << (settings.showProxyMagazines ? "1" : "0") << std::endl;
            file << "showBackpacks=" << (settings.showBackpacks ? "1" : "0") << std::endl;
            file << "showRare=" << (settings.showRare ? "1" : "0") << std::endl;
            file << "showFood=" << (settings.showFood ? "1" : "0") << std::endl;
            file << "showAmmo=" << (settings.showAmmo ? "1" : "0") << std::endl;
            file << "showPlayerList=" << (settings.showPlayerList ? "1" : "0") << std::endl;
            file << "showServerPlayerList=" << (settings.showServerPlayerList ? "1" : "0") << std::endl;
            file << "showZombies=" << (settings.showZombies ? "1" : "0") << std::endl;
            file << "showAnimals=" << (settings.showAnimals ? "1" : "0") << std::endl;
            file << "playername=" << settings.playername << std::endl;
            file << "BlipSize=" << settings.BlipSize << std::endl;
            file << "BlipSize2=" << settings.BlipSize2 << std::endl;
            file << "Aimlinelength=" << settings.Aimlinelength << std::endl;
            file << "showOptics=" << (settings.showOptics ? "1" : "0") << std::endl;
            file << "showBase=" << (settings.showBase ? "1" : "0") << std::endl;
            file << "showMelee=" << (settings.showMelee ? "1" : "0") << std::endl;
            file << "LootDistanceDeadzone=" << settings.LootDistanceDeadzone << std::endl;
            file << "showExplosives=" << (settings.showExplosives ? "1" : "0") << std::endl;
            file << "showContainer=" << (settings.showContainer ? "1" : "0") << std::endl;
            file << "showCooking=" << (settings.showCooking ? "1" : "0") << std::endl;
            file << "showCamping=" << (settings.showCamping ? "1" : "0") << std::endl;
            file << "showStash=" << (settings.showStash ? "1" : "0") << std::endl;
            file << "ZombiesBlipSize=" << settings.ZombiesBlipSize << std::endl;
            file << "AnimalsBlipSize=" << settings.AnimalsBlipSize << std::endl;
            file << "FontRADAR=" << settings.FontRADAR << std::endl;
            file << "FontRADAR2=" << settings.FontRADAR2 << std::endl;
            file << "showPlayerNameRadar=" << (settings.showPlayerNameRadar ? "1" : "0") << std::endl;
            file << "showPlayerDistanceRadar=" << (settings.showPlayerDistanceRadar ? "1" : "0") << std::endl;
            file << "showPlayerHandRadar=" << (settings.showPlayerHandRadar ? "1" : "0") << std::endl;

            file << std::endl << "[ESP]" << std::endl;
            file << "showDeadPlayersFUSER=" << (settings.showDeadPlayersFUSER ? "1" : "0") << std::endl;
            file << "showVehiclesFUSER=" << (settings.showVehiclesFUSER ? "1" : "0") << std::endl;
            file << "showGrounditemsFUSER=" << (settings.showGrounditemsFUSER ? "1" : "0") << std::endl;
            file << "showBoatsFUSER=" << (settings.showBoatsFUSER ? "1" : "0") << std::endl;
            file << "showDeadAnimalsFUSER=" << (settings.showDeadAnimalsFUSER ? "1" : "0") << std::endl;
            file << "showClothingFUSER=" << (settings.showClothingFUSER ? "1" : "0") << std::endl;
            file << "showWeaponsFUSER=" << (settings.showWeaponsFUSER ? "1" : "0") << std::endl;
            file << "showProxyMagazinesFUSER=" << (settings.showProxyMagazinesFUSER ? "1" : "0") << std::endl;
            file << "showBackpacksFUSER=" << (settings.showBackpacksFUSER ? "1" : "0") << std::endl;
            file << "showRareFUSER=" << (settings.showRareFUSER ? "1" : "0") << std::endl;
            file << "showFoodFUSER=" << (settings.showFoodFUSER ? "1" : "0") << std::endl;
            file << "showAmmoFUSER=" << (settings.showAmmoFUSER ? "1" : "0") << std::endl;
            file << "showZombiesFUSER=" << (settings.showZombiesFUSER ? "1" : "0") << std::endl;
            file << "showAnimalsFUSER=" << (settings.showAnimalsFUSER ? "1" : "0") << std::endl;
            file << "lootDistanceFUSER=" << settings.lootDistanceFUSER << std::endl;
            file << "ZombieDistanceFUSER=" << settings.ZombieDistanceFUSER << std::endl;
            file << "showPlayerInfoesp=" << (settings.showPlayerInfoesp ? "1" : "0") << std::endl;
            file << "showPlayerNameFUSER=" << (settings.showPlayerNameFUSER ? "1" : "0") << std::endl;
            file << "showPlayerDistanceFUSER=" << (settings.showPlayerDistanceFUSER ? "1" : "0") << std::endl;
            file << "showPlayerHandFUSER=" << (settings.showPlayerHandFUSER ? "1" : "0") << std::endl;
            file << "showPlayerBoxFUSER=" << (settings.showPlayerBoxFUSER ? "1" : "0") << std::endl;
            file << "showAnimalBoxFUSER=" << (settings.showAnimalBoxFUSER ? "1" : "0") << std::endl;
            file << "showAnimalNameFUSER=" << (settings.showAnimalNameFUSER ? "1" : "0") << std::endl;
            file << "showZombieBoxFUSER=" << (settings.showZombieBoxFUSER ? "1" : "0") << std::endl;
            file << "showZombieNameFUSER=" << (settings.showZombieNameFUSER ? "1" : "0") << std::endl;
            file << "showZombieDistanceFUSER=" << (settings.showZombieDistanceFUSER ? "1" : "0") << std::endl;
            file << "showOpticsFUSER=" << (settings.showOpticsFUSER ? "1" : "0") << std::endl;
            file << "showDebugFUSER=" << (settings.showDebugFUSER ? "1" : "0") << std::endl;

            // Entity distance settings
            file << "animalMaxDistance=" << settings.animalMaxDistance << std::endl;
            file << "vehicleMaxDistance=" << settings.vehicleMaxDistance << std::endl;
            file << "boatMaxDistance=" << settings.boatMaxDistance << std::endl;
            file << "deadPlayerMaxDistance=" << settings.deadPlayerMaxDistance << std::endl;
            file << "deadAnimalMaxDistance=" << settings.deadAnimalMaxDistance << std::endl;
            file << "playerMaxDistance=" << settings.playerMaxDistance << std::endl;
            file << "showBaseFUSER=" << (settings.showBaseFUSER ? "1" : "0") << std::endl;
            file << "showMeleeFUSER=" << (settings.showMeleeFUSER ? "1" : "0") << std::endl;
            file << "showExplosivesFUSER=" << (settings.showExplosivesFUSER ? "1" : "0") << std::endl;
            file << "showContainerFUSER=" << (settings.showContainerFUSER ? "1" : "0") << std::endl;
            file << "showCookingFUSER=" << (settings.showCookingFUSER ? "1" : "0") << std::endl;
            file << "showCampingFUSER=" << (settings.showCampingFUSER ? "1" : "0") << std::endl;
            file << "showStashFUSER=" << (settings.showStashFUSER ? "1" : "0") << std::endl;
            file << "LootDebugDistance=" << settings.LootDebugDistance << std::endl;
            file << "enableESPDebugLogging=" << (settings.enableESPDebugLogging ? "1" : "0") << std::endl;
            file << "espTextSize=" << settings.espTextSize << std::endl;
            file << "baseFontSize=" << settings.baseFontSize << std::endl;
            file << "selectedFontIndex=" << settings.selectedFontIndex << std::endl;
            file << "fontBold=" << (settings.fontBold ? "1" : "0") << std::endl;
            file << "fontOutline=" << (settings.fontOutline ? "1" : "0") << std::endl;
            file << "fontShadow=" << (settings.fontShadow ? "1" : "0") << std::endl;
            file << "fontAlpha=" << settings.fontAlpha << std::endl;
            file << "fontShadowOffset=" << settings.fontShadowOffset << std::endl;
            file << "fontOutlineSize=" << settings.fontOutlineSize << std::endl;
            
            // Font quality settings
            file << "fontOversampleH=" << settings.fontOversampleH << std::endl;
            file << "fontOversampleV=" << settings.fontOversampleV << std::endl;
            file << "fontRasterizerMultiply=" << settings.fontRasterizerMultiply << std::endl;
            file << "fontPixelSnap=" << (settings.fontPixelSnap ? "1" : "0") << std::endl;
            file << "fontAtlasSize=" << settings.fontAtlasSize << std::endl;
            file << "fontUIBias=" << settings.fontUIBias << std::endl;
            file << "fpsCounterFontSize=" << settings.fpsCounterFontSize << std::endl;
            file << "fpsCounterHorizontalPadding=" << settings.fpsCounterHorizontalPadding << std::endl;
            file << "fpsCounterVerticalPadding=" << settings.fpsCounterVerticalPadding << std::endl;
            
            file << "boundingBoxThickness=" << settings.boundingBoxThickness << std::endl;
            file << "boundingBoxAntiAliasing=" << (settings.boundingBoxAntiAliasing ? "1" : "0") << std::endl;
            file << "boundingBoxRounded=" << (settings.boundingBoxRounded ? "1" : "0") << std::endl;
            file << "boundingBoxRounding=" << settings.boundingBoxRounding << std::endl;
            file << "itemESPToggleKey=" << settings.itemESPToggleKey << std::endl;
            file << "itemESPEnabled=" << (settings.itemESPEnabled ? "1" : "0") << std::endl;

            // ESP Color Settings
            file << std::endl << "[ESPColors]" << std::endl;

            // Player Colors
            file << "playerBoxColorR=" << settings.playerBoxColorR << std::endl;
            file << "playerBoxColorG=" << settings.playerBoxColorG << std::endl;
            file << "playerBoxColorB=" << settings.playerBoxColorB << std::endl;
            file << "playerBoxColorA=" << settings.playerBoxColorA << std::endl;
            file << "playerTextColorR=" << settings.playerTextColorR << std::endl;
            file << "playerTextColorG=" << settings.playerTextColorG << std::endl;
            file << "playerTextColorB=" << settings.playerTextColorB << std::endl;
            file << "playerTextColorA=" << settings.playerTextColorA << std::endl;
            // Individual Player Text Colors
            file << "playerNameColorR=" << settings.playerNameColorR << std::endl;
            file << "playerNameColorG=" << settings.playerNameColorG << std::endl;
            file << "playerNameColorB=" << settings.playerNameColorB << std::endl;
            file << "playerNameColorA=" << settings.playerNameColorA << std::endl;
            file << "playerDistanceColorR=" << settings.playerDistanceColorR << std::endl;
            file << "playerDistanceColorG=" << settings.playerDistanceColorG << std::endl;
            file << "playerDistanceColorB=" << settings.playerDistanceColorB << std::endl;
            file << "playerDistanceColorA=" << settings.playerDistanceColorA << std::endl;
            file << "playerHandColorR=" << settings.playerHandColorR << std::endl;
            file << "playerHandColorG=" << settings.playerHandColorG << std::endl;
            file << "playerHandColorB=" << settings.playerHandColorB << std::endl;
            file << "playerHandColorA=" << settings.playerHandColorA << std::endl;
            file << "playerSkeletonColorR=" << settings.playerSkeletonColorR << std::endl;
            file << "playerSkeletonColorG=" << settings.playerSkeletonColorG << std::endl;
            file << "playerSkeletonColorB=" << settings.playerSkeletonColorB << std::endl;
            file << "playerSkeletonColorA=" << settings.playerSkeletonColorA << std::endl;

            // Zombie Colors
            file << "zombieBoxColorR=" << settings.zombieBoxColorR << std::endl;
            file << "zombieBoxColorG=" << settings.zombieBoxColorG << std::endl;
            file << "zombieBoxColorB=" << settings.zombieBoxColorB << std::endl;
            file << "zombieBoxColorA=" << settings.zombieBoxColorA << std::endl;
            file << "zombieTextColorR=" << settings.zombieTextColorR << std::endl;
            file << "zombieTextColorG=" << settings.zombieTextColorG << std::endl;
            file << "zombieTextColorB=" << settings.zombieTextColorB << std::endl;
            file << "zombieTextColorA=" << settings.zombieTextColorA << std::endl;
            file << "zombieSkeletonColorR=" << settings.zombieSkeletonColorR << std::endl;
            file << "zombieSkeletonColorG=" << settings.zombieSkeletonColorG << std::endl;
            file << "zombieSkeletonColorB=" << settings.zombieSkeletonColorB << std::endl;
            file << "zombieSkeletonColorA=" << settings.zombieSkeletonColorA << std::endl;

            // Animal Colors
            file << "animalBoxColorR=" << settings.animalBoxColorR << std::endl;
            file << "animalBoxColorG=" << settings.animalBoxColorG << std::endl;
            file << "animalBoxColorB=" << settings.animalBoxColorB << std::endl;
            file << "animalBoxColorA=" << settings.animalBoxColorA << std::endl;
            file << "animalTextColorR=" << settings.animalTextColorR << std::endl;
            file << "animalTextColorG=" << settings.animalTextColorG << std::endl;
            file << "animalTextColorB=" << settings.animalTextColorB << std::endl;
            file << "animalTextColorA=" << settings.animalTextColorA << std::endl;

            // Dead Player Colors
            file << "deadPlayerBoxColorR=" << settings.deadPlayerBoxColorR << std::endl;
            file << "deadPlayerBoxColorG=" << settings.deadPlayerBoxColorG << std::endl;
            file << "deadPlayerBoxColorB=" << settings.deadPlayerBoxColorB << std::endl;
            file << "deadPlayerBoxColorA=" << settings.deadPlayerBoxColorA << std::endl;
            file << "deadPlayerTextColorR=" << settings.deadPlayerTextColorR << std::endl;
            file << "deadPlayerTextColorG=" << settings.deadPlayerTextColorG << std::endl;
            file << "deadPlayerTextColorB=" << settings.deadPlayerTextColorB << std::endl;
            file << "deadPlayerTextColorA=" << settings.deadPlayerTextColorA << std::endl;

            // Vehicle Colors
            file << "vehicleBoxColorR=" << settings.vehicleBoxColorR << std::endl;
            file << "vehicleBoxColorG=" << settings.vehicleBoxColorG << std::endl;
            file << "vehicleBoxColorB=" << settings.vehicleBoxColorB << std::endl;
            file << "vehicleBoxColorA=" << settings.vehicleBoxColorA << std::endl;
            file << "vehicleTextColorR=" << settings.vehicleTextColorR << std::endl;
            file << "vehicleTextColorG=" << settings.vehicleTextColorG << std::endl;
            file << "vehicleTextColorB=" << settings.vehicleTextColorB << std::endl;
            file << "vehicleTextColorA=" << settings.vehicleTextColorA << std::endl;

            // Health Bar Colors
            file << "healthBarHighColorR=" << settings.healthBarHighColorR << std::endl;
            file << "healthBarHighColorG=" << settings.healthBarHighColorG << std::endl;
            file << "healthBarHighColorB=" << settings.healthBarHighColorB << std::endl;
            file << "healthBarHighColorA=" << settings.healthBarHighColorA << std::endl;
            file << "healthBarMediumColorR=" << settings.healthBarMediumColorR << std::endl;
            file << "healthBarMediumColorG=" << settings.healthBarMediumColorG << std::endl;
            file << "healthBarMediumColorB=" << settings.healthBarMediumColorB << std::endl;
            file << "healthBarMediumColorA=" << settings.healthBarMediumColorA << std::endl;
            file << "healthBarLowColorR=" << settings.healthBarLowColorR << std::endl;
            file << "healthBarLowColorG=" << settings.healthBarLowColorG << std::endl;
            file << "healthBarLowColorB=" << settings.healthBarLowColorB << std::endl;
            file << "healthBarLowColorA=" << settings.healthBarLowColorA << std::endl;
            file << "healthBarBackgroundColorR=" << settings.healthBarBackgroundColorR << std::endl;
            file << "healthBarBackgroundColorG=" << settings.healthBarBackgroundColorG << std::endl;
            file << "healthBarBackgroundColorB=" << settings.healthBarBackgroundColorB << std::endl;
            file << "healthBarBackgroundColorA=" << settings.healthBarBackgroundColorA << std::endl;

            // Container Contents Colors
            file << "containerContentsColorR=" << settings.containerContentsColorR << std::endl;
            file << "containerContentsColorG=" << settings.containerContentsColorG << std::endl;
            file << "containerContentsColorB=" << settings.containerContentsColorB << std::endl;
            file << "containerContentsColorA=" << settings.containerContentsColorA << std::endl;

            // Loot Category Colors
            file << "weaponColorR=" << settings.weaponColorR << std::endl;
            file << "weaponColorG=" << settings.weaponColorG << std::endl;
            file << "weaponColorB=" << settings.weaponColorB << std::endl;
            file << "weaponColorA=" << settings.weaponColorA << std::endl;
            file << "clothingColorR=" << settings.clothingColorR << std::endl;
            file << "clothingColorG=" << settings.clothingColorG << std::endl;
            file << "clothingColorB=" << settings.clothingColorB << std::endl;
            file << "clothingColorA=" << settings.clothingColorA << std::endl;
            file << "backpackColorR=" << settings.backpackColorR << std::endl;
            file << "backpackColorG=" << settings.backpackColorG << std::endl;
            file << "backpackColorB=" << settings.backpackColorB << std::endl;
            file << "backpackColorA=" << settings.backpackColorA << std::endl;
            file << "foodColorR=" << settings.foodColorR << std::endl;
            file << "foodColorG=" << settings.foodColorG << std::endl;
            file << "foodColorB=" << settings.foodColorB << std::endl;
            file << "foodColorA=" << settings.foodColorA << std::endl;
            file << "ammoColorR=" << settings.ammoColorR << std::endl;
            file << "ammoColorG=" << settings.ammoColorG << std::endl;
            file << "ammoColorB=" << settings.ammoColorB << std::endl;
            file << "ammoColorA=" << settings.ammoColorA << std::endl;
            file << "proxyMagazineColorR=" << settings.proxyMagazineColorR << std::endl;
            file << "proxyMagazineColorG=" << settings.proxyMagazineColorG << std::endl;
            file << "proxyMagazineColorB=" << settings.proxyMagazineColorB << std::endl;
            file << "proxyMagazineColorA=" << settings.proxyMagazineColorA << std::endl;
            file << "opticColorR=" << settings.opticColorR << std::endl;
            file << "opticColorG=" << settings.opticColorG << std::endl;
            file << "opticColorB=" << settings.opticColorB << std::endl;
            file << "opticColorA=" << settings.opticColorA << std::endl;
            file << "baseBuildingColorR=" << settings.baseBuildingColorR << std::endl;
            file << "baseBuildingColorG=" << settings.baseBuildingColorG << std::endl;
            file << "baseBuildingColorB=" << settings.baseBuildingColorB << std::endl;
            file << "baseBuildingColorA=" << settings.baseBuildingColorA << std::endl;
            file << "meleeColorR=" << settings.meleeColorR << std::endl;
            file << "meleeColorG=" << settings.meleeColorG << std::endl;
            file << "meleeColorB=" << settings.meleeColorB << std::endl;
            file << "meleeColorA=" << settings.meleeColorA << std::endl;
            file << "explosiveColorR=" << settings.explosiveColorR << std::endl;
            file << "explosiveColorG=" << settings.explosiveColorG << std::endl;
            file << "explosiveColorB=" << settings.explosiveColorB << std::endl;
            file << "explosiveColorA=" << settings.explosiveColorA << std::endl;
            file << "containerColorR=" << settings.containerColorR << std::endl;
            file << "containerColorG=" << settings.containerColorG << std::endl;
            file << "containerColorB=" << settings.containerColorB << std::endl;
            file << "containerColorA=" << settings.containerColorA << std::endl;
            file << "cookingColorR=" << settings.cookingColorR << std::endl;
            file << "cookingColorG=" << settings.cookingColorG << std::endl;
            file << "cookingColorB=" << settings.cookingColorB << std::endl;
            file << "cookingColorA=" << settings.cookingColorA << std::endl;
            file << "campingColorR=" << settings.campingColorR << std::endl;
            file << "campingColorG=" << settings.campingColorG << std::endl;
            file << "campingColorB=" << settings.campingColorB << std::endl;
            file << "campingColorA=" << settings.campingColorA << std::endl;
            file << "stashColorR=" << settings.stashColorR << std::endl;
            file << "stashColorG=" << settings.stashColorG << std::endl;
            file << "stashColorB=" << settings.stashColorB << std::endl;
            file << "stashColorA=" << settings.stashColorA << std::endl;
            file << "groundItemColorR=" << settings.groundItemColorR << std::endl;
            file << "groundItemColorG=" << settings.groundItemColorG << std::endl;
            file << "groundItemColorB=" << settings.groundItemColorB << std::endl;
            file << "groundItemColorA=" << settings.groundItemColorA << std::endl;

            file << std::endl << "[UI]" << std::endl;
            file << "followPlayerEnabled=" << (settings.followPlayerEnabled ? "1" : "0") << std::endl;

            fflush(stdout);
            file << std::endl << "[PlayerExclusion]" << std::endl;
            file << "excludedPlayerCount=" << settings.excludedPlayerSteamIDs.size() << std::endl;
            fflush(stdout);
            for (size_t i = 0; i < settings.excludedPlayerSteamIDs.size(); ++i) {
                file << "excludedPlayer" << i << "=" << settings.excludedPlayerSteamIDs[i] << std::endl;
                fflush(stdout);
            }

            file << std::endl << "[Performance]" << std::endl;
            file << "vSyncEnabled=" << (settings.vSyncEnabled ? "1" : "0") << std::endl;

            file << std::endl << "[MultiCore]" << std::endl;
            file << "multiCoreProfile=" << settings.multiCoreProfile << std::endl;
            file << "enableParallelProcessing=" << (settings.enableParallelProcessing ? "1" : "0") << std::endl;
            file << "enableThreadAffinity=" << (settings.enableThreadAffinity ? "1" : "0") << std::endl;
            file << "adaptiveTimings=" << (settings.adaptiveTimings ? "1" : "0") << std::endl;
            file << "maxWorkerThreads=" << settings.maxWorkerThreads << std::endl;

            file << std::endl << "[HealthBar]" << std::endl;
            file << "playerHealthBarEnabled=" << (settings.playerHealthBarEnabled ? "1" : "0") << std::endl;
            file << "zombieHealthBarEnabled=" << (settings.zombieHealthBarEnabled ? "1" : "0") << std::endl;
            file << "healthBarWidth=" << settings.healthBarWidth << std::endl;
            file << "healthBarHeight=" << settings.healthBarHeight << std::endl;
            file << "healthBarOffsetY=" << settings.healthBarOffsetY << std::endl;
            file << "healthBarMaxDistance=" << settings.healthBarMaxDistance << std::endl;
            file << "healthBarType=" << settings.healthBarType << std::endl;
            file << "showHealthNumbers=" << (settings.showHealthNumbers ? "1" : "0") << std::endl;
            file << "showHealthBar=" << (settings.showHealthBar ? "1" : "0") << std::endl;
            file << "showAllHealthStats=" << (settings.showAllHealthStats ? "1" : "0") << std::endl;

            file << std::endl << "[SkeletonESP]" << std::endl;
            file << "playerSkeletonEnabled=" << (settings.playerSkeletonEnabled ? "1" : "0") << std::endl;
            file << "zombieSkeletonEnabled=" << (settings.zombieSkeletonEnabled ? "1" : "0") << std::endl;
            file << "skeletonLineThickness=" << settings.skeletonLineThickness << std::endl;
            file << "playerSkeletonMaxDistance=" << settings.playerSkeletonMaxDistance << std::endl;
            file << "zombieSkeletonMaxDistance=" << settings.zombieSkeletonMaxDistance << std::endl;
            file << "skeletonDetailLevel=" << settings.skeletonDetailLevel << std::endl;

            file << std::endl << "[PersonalHealth]" << std::endl;
            file << "showPersonalHealth=" << (settings.showPersonalHealth ? "1" : "0") << std::endl;
            file << "personalHealthPosition=" << settings.personalHealthPosition << std::endl;
            file << "personalHealthScale=" << settings.personalHealthScale << std::endl;
            file << "personalHealthBackground=" << (settings.personalHealthBackground ? "1" : "0") << std::endl;
            file << "useIndividualPositioning=" << (settings.useIndividualPositioning ? "1" : "0") << std::endl;
            file << "healthIndicatorX=" << settings.healthIndicatorX << std::endl;
            file << "healthIndicatorY=" << settings.healthIndicatorY << std::endl;
            file << "bloodIndicatorX=" << settings.bloodIndicatorX << std::endl;
            file << "bloodIndicatorY=" << settings.bloodIndicatorY << std::endl;
            file << "shockIndicatorX=" << settings.shockIndicatorX << std::endl;
            file << "shockIndicatorY=" << settings.shockIndicatorY << std::endl;
            file << "statusIndicatorX=" << settings.statusIndicatorX << std::endl;
            file << "statusIndicatorY=" << settings.statusIndicatorY << std::endl;
            file << "showHealthIndicator=" << (settings.showHealthIndicator ? "1" : "0") << std::endl;
            file << "showBloodIndicator=" << (settings.showBloodIndicator ? "1" : "0") << std::endl;
            file << "showShockIndicator=" << (settings.showShockIndicator ? "1" : "0") << std::endl;
            file << "showStatusIndicator=" << (settings.showStatusIndicator ? "1" : "0") << std::endl;
            file << "showHealthLabels=" << (settings.showHealthLabels ? "1" : "0") << std::endl;
            file << "useCustomFont=" << (settings.useCustomFont ? "1" : "0") << std::endl;

            file << std::endl << "[ContainerContents]" << std::endl;
            file << "showContainerContents=" << (settings.showContainerContents ? "1" : "0") << std::endl;
            file << "containerContentsMaxItems=" << settings.containerContentsMaxItems << std::endl;
            file << "containerContentsMaxDistance=" << settings.containerContentsMaxDistance << std::endl;

            file << std::endl << "[FontSizes]" << std::endl;
            file << "playerNameFontSize=" << settings.playerNameFontSize << std::endl;
            file << "itemFontSize=" << settings.itemFontSize << std::endl;
            file << "distanceFontSize=" << settings.distanceFontSize << std::endl;

            file << std::endl << "[Crosshair]" << std::endl;
            file << "crosshairEnabled=" << (settings.crosshairEnabled ? "1" : "0") << std::endl;
            file << "crosshairSize=" << settings.crosshairSize << std::endl;
            file << "crosshairThickness=" << settings.crosshairThickness << std::endl;
            file << "crosshairGap=" << settings.crosshairGap << std::endl;
            file << "crosshairAlpha=" << settings.crosshairAlpha << std::endl;
            file << "crosshairOutline=" << (settings.crosshairOutline ? "1" : "0") << std::endl;
            file << "crosshairOutlineThickness=" << settings.crosshairOutlineThickness << std::endl;
            file << "crosshairDot=" << (settings.crosshairDot ? "1" : "0") << std::endl;
            file << "crosshairDotSize=" << settings.crosshairDotSize << std::endl;
            file << "crosshairUseAimPoint=" << (settings.crosshairUseAimPoint ? "1" : "0") << std::endl;
            file << "crosshairVerticalOffset=" << settings.crosshairVerticalOffset << std::endl;
            file << "crosshairColorR=" << settings.crosshairColorR << std::endl;
            file << "crosshairColorG=" << settings.crosshairColorG << std::endl;
            file << "crosshairColorB=" << settings.crosshairColorB << std::endl;
            file << "crosshairColorA=" << settings.crosshairColorA << std::endl;
            file << "crosshairOutlineColorR=" << settings.crosshairOutlineColorR << std::endl;
            file << "crosshairOutlineColorG=" << settings.crosshairOutlineColorG << std::endl;
            file << "crosshairOutlineColorB=" << settings.crosshairOutlineColorB << std::endl;
            file << "crosshairOutlineColorA=" << settings.crosshairOutlineColorA << std::endl;

            file.close();
            printf("Settings saved to: %s\n", settingsFilePath.c_str());
            fflush(stdout);
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Error saving settings: " << e.what() << std::endl;
            return false;
        }
    }

    bool SettingsManager::loadSettings() {
        fflush(stdout);
        try {
            std::ifstream file(settingsFilePath);
            if (!file.is_open()) {
                printf("Settings file not found, using defaults: %s\n", settingsFilePath.c_str());
                fflush(stdout);
                return false; // File doesn't exist, use defaults
            }

            std::string line;
            std::string currentSection;

            while (std::getline(file, line)) {
                // Remove whitespace
                line.erase(0, line.find_first_not_of(" \t"));
                line.erase(line.find_last_not_of(" \t") + 1);

                // Skip empty lines and comments
                if (line.empty() || line[0] == '#' || line[0] == ';') {
                    continue;
                }

                // Check for section headers
                if (line[0] == '[' && line.back() == ']') {
                    currentSection = line.substr(1, line.length() - 2);
                    fflush(stdout);
                    continue;
                }

                // Parse key=value pairs
                size_t equalPos = line.find('=');
                if (equalPos == std::string::npos) {
                    continue;
                }

                std::string key = line.substr(0, equalPos);
                std::string value = line.substr(equalPos + 1);

                fflush(stdout);

                // Parse the settings based on key
                parseSettingValue(key, value);
            }

            file.close();
            printf("Settings loaded from: %s\n", settingsFilePath.c_str());
            fflush(stdout);
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Error loading settings: " << e.what() << std::endl;
            return false;
        }
    }

    void SettingsManager::parseSettingValue(const std::string& key, const std::string& value) {
        // Helper function to convert string to bool
        auto toBool = [](const std::string& str) { return str == "1" || str == "true"; };
        auto toInt = [](const std::string& str) { return std::stoi(str); };
        auto toFloat = [](const std::string& str) { return std::stof(str); };

        // Radar settings
        if (key == "showDeadPlayers") settings.showDeadPlayers = toBool(value);
        else if (key == "showVehicles") settings.showVehicles = toBool(value);
        else if (key == "showGrounditems") settings.showGrounditems = toBool(value);
        else if (key == "showBoats") settings.showBoats = toBool(value);
        else if (key == "showDeadAnimals") settings.showDeadAnimals = toBool(value);
        else if (key == "showClothing") settings.showClothing = toBool(value);
        else if (key == "showWeapons") settings.showWeapons = toBool(value);
        else if (key == "showProxyMagazines") settings.showProxyMagazines = toBool(value);
        else if (key == "showBackpacks") settings.showBackpacks = toBool(value);
        else if (key == "showRare") settings.showRare = toBool(value);
        else if (key == "showFood") settings.showFood = toBool(value);
        else if (key == "showAmmo") settings.showAmmo = toBool(value);
        else if (key == "showPlayerList") settings.showPlayerList = toBool(value);
        else if (key == "showServerPlayerList") settings.showServerPlayerList = toBool(value);
        else if (key == "showZombies") settings.showZombies = toBool(value);
        else if (key == "showAnimals") settings.showAnimals = toBool(value);
        else if (key == "playername") settings.playername = value;
        else if (key == "BlipSize") settings.BlipSize = toInt(value);
        else if (key == "BlipSize2") settings.BlipSize2 = toInt(value);
        else if (key == "Aimlinelength") settings.Aimlinelength = toInt(value);
        else if (key == "showOptics") settings.showOptics = toBool(value);
        else if (key == "showBase") settings.showBase = toBool(value);
        else if (key == "showMelee") settings.showMelee = toBool(value);
        else if (key == "showContainer") settings.showContainer = toBool(value);
        else if (key == "showCooking") settings.showCooking = toBool(value);
        else if (key == "showCamping") settings.showCamping = toBool(value);
        else if (key == "showStash") settings.showStash = toBool(value);
        else if (key == "LootDistanceDeadzone") settings.LootDistanceDeadzone = toInt(value);
        else if (key == "showExplosives") settings.showExplosives = toBool(value);
        else if (key == "ZombiesBlipSize") settings.ZombiesBlipSize = toInt(value);
        else if (key == "AnimalsBlipSize") settings.AnimalsBlipSize = toInt(value);
        else if (key == "FontRADAR") settings.FontRADAR = toInt(value);
        else if (key == "FontRADAR2") settings.FontRADAR2 = toInt(value);
        else if (key == "showPlayerNameRadar") settings.showPlayerNameRadar = toBool(value);
        else if (key == "showPlayerDistanceRadar") settings.showPlayerDistanceRadar = toBool(value);
        else if (key == "showPlayerHandRadar") settings.showPlayerHandRadar = toBool(value);

        // ESP Color settings and other complex settings - use separate function to avoid nesting limit
        else {
            parseESPAndOtherSettings(key, value, settings);
        }

    }

    void SettingsManager::applyToRenderBridge(RenderBridge* bridge) {
        if (!bridge) return;

        // Apply radar settings
        bridge->setShowDeadPlayers(settings.showDeadPlayers);
        bridge->setShowVehicles(settings.showVehicles);
        bridge->setShowGrounditems(settings.showGrounditems);
        bridge->setShowBoats(settings.showBoats);
        bridge->setShowDeadAnimals(settings.showDeadAnimals);
        bridge->setShowClothing(settings.showClothing);
        bridge->setShowWeapons(settings.showWeapons);
        bridge->setShowProxyMagazines(settings.showProxyMagazines);
        bridge->setShowBackpacks(settings.showBackpacks);
        bridge->setShowRare(settings.showRare);
        bridge->setShowFood(settings.showFood);
        bridge->setShowAmmo(settings.showAmmo);
        bridge->setShowPlayerList(settings.showPlayerList);
        bridge->setShowServerPlayerList(settings.showServerPlayerList);
        bridge->setShowZombies(settings.showZombies);
        bridge->setShowAnimals(settings.showAnimals);
        bridge->setShowPlayerName(settings.playername);
        bridge->setShowBlipSize(settings.BlipSize);
        bridge->setShowBlipSize2(settings.BlipSize2);
        bridge->setAimlinelength(settings.Aimlinelength);
        bridge->setShowOptics(settings.showOptics);
        bridge->setShowBase(settings.showBase);
        bridge->setShowMelee(settings.showMelee);
        bridge->setShowContainer(settings.showContainer);
        bridge->setShowCooking(settings.showCooking);
        bridge->setShowCamping(settings.showCamping);
        bridge->setShowStash(settings.showStash);
        bridge->setLootDistanceDeadzone(settings.LootDistanceDeadzone);
        bridge->setShowExplosives(settings.showExplosives);
        bridge->setZombiesBlipSize(settings.ZombiesBlipSize);
        bridge->setAnimalsBlipSize(settings.AnimalsBlipSize);
        bridge->setRadarFont(settings.FontRADAR);
        bridge->setRadarFont2(settings.FontRADAR2);
        bridge->setShowPlayerNameRadar(settings.showPlayerNameRadar);
        bridge->setShowPlayerDistanceRadar(settings.showPlayerDistanceRadar);
        bridge->setShowPlayerHandRadar(settings.showPlayerHandRadar);

        // Apply ESP settings
        bridge->setShowDeadPlayersFUSER(settings.showDeadPlayersFUSER);
        bridge->setShowVehiclesFUSER(settings.showVehiclesFUSER);
        bridge->setShowGrounditemsFUSER(settings.showGrounditemsFUSER);
        bridge->setShowBoatsFUSER(settings.showBoatsFUSER);
        bridge->setShowDeadAnimalsFUSER(settings.showDeadAnimalsFUSER);
        bridge->setShowClothingFUSER(settings.showClothingFUSER);
        bridge->setShowWeaponsFUSER(settings.showWeaponsFUSER);
        bridge->setShowProxyMagazinesFUSER(settings.showProxyMagazinesFUSER);
        bridge->setShowBackpacksFUSER(settings.showBackpacksFUSER);
        bridge->setShowRareFUSER(settings.showRareFUSER);
        bridge->setShowFoodFUSER(settings.showFoodFUSER);
        bridge->setShowAmmoFUSER(settings.showAmmoFUSER);
        bridge->setShowZombiesFUSER(settings.showZombiesFUSER);
        bridge->setShowAnimalsFUSER(settings.showAnimalsFUSER);
        bridge->setlootDistance(settings.lootDistanceFUSER);
        bridge->setZombieDistance(settings.ZombieDistanceFUSER);
        bridge->setShowPlayerInfoesp(settings.showPlayerInfoesp);
        bridge->setShowPlayerNameFUSER(settings.showPlayerNameFUSER);
        bridge->setShowPlayerDistanceFUSER(settings.showPlayerDistanceFUSER);
        bridge->setShowPlayerHandFUSER(settings.showPlayerHandFUSER);
        bridge->setShowPlayerBoxFUSER(settings.showPlayerBoxFUSER);
        bridge->setShowAnimalBoxFUSER(settings.showAnimalBoxFUSER);
        bridge->setShowAnimalNameFUSER(settings.showAnimalNameFUSER);
        bridge->setShowZombieBoxFUSER(settings.showZombieBoxFUSER);
        bridge->setShowZombieNameFUSER(settings.showZombieNameFUSER);
        bridge->setShowZombieDistanceFUSER(settings.showZombieDistanceFUSER);
        bridge->setShowOpticsFUSER(settings.showOpticsFUSER);
        bridge->setShowDebugFUSER(settings.showDebugFUSER);

        // Apply entity distance settings
        bridge->setAnimalMaxDistance(settings.animalMaxDistance);
        bridge->setVehicleMaxDistance(settings.vehicleMaxDistance);
        bridge->setBoatMaxDistance(settings.boatMaxDistance);
        bridge->setDeadPlayerMaxDistance(settings.deadPlayerMaxDistance);
        bridge->setDeadAnimalMaxDistance(settings.deadAnimalMaxDistance);
        bridge->setPlayerMaxDistance(settings.playerMaxDistance);
        bridge->setShowBaseFUSER(settings.showBaseFUSER);
        bridge->setShowMeleeFUSER(settings.showMeleeFUSER);
        bridge->setShowExplosivesFUSER(settings.showExplosivesFUSER);
        bridge->setShowContainerFUSER(settings.showContainerFUSER);
        bridge->setShowCookingFUSER(settings.showCookingFUSER);
        bridge->setShowCampingFUSER(settings.showCampingFUSER);
        bridge->setShowStashFUSER(settings.showStashFUSER);
        bridge->setLootDebugDistance(settings.LootDebugDistance);
        bridge->setEnableESPDebugLogging(settings.enableESPDebugLogging);
        bridge->setESPTextSize(settings.espTextSize);
        bridge->setBaseFontSize(settings.baseFontSize);
        bridge->setSelectedFontIndex(settings.selectedFontIndex);
        bridge->setFontBold(settings.fontBold);
        bridge->setFontOutline(settings.fontOutline);
        bridge->setFontShadow(settings.fontShadow);
        bridge->setFontAlpha(settings.fontAlpha);
        bridge->setFontShadowOffset(settings.fontShadowOffset);
        bridge->setFontOutlineSize(settings.fontOutlineSize);
        bridge->setFontOversampleH(settings.fontOversampleH);
        bridge->setFontOversampleV(settings.fontOversampleV);
        bridge->setFontRasterizerMultiply(settings.fontRasterizerMultiply);
        bridge->setFontPixelSnap(settings.fontPixelSnap);
        bridge->setFontAtlasSize(settings.fontAtlasSize);
        bridge->setFpsCounterFontSize(settings.fpsCounterFontSize);
        bridge->setFpsCounterHorizontalPadding(settings.fpsCounterHorizontalPadding);
        bridge->setFpsCounterVerticalPadding(settings.fpsCounterVerticalPadding);
        bridge->setBoundingBoxThickness(settings.boundingBoxThickness);
        bridge->setBoundingBoxAntiAliasing(settings.boundingBoxAntiAliasing);
        bridge->setBoundingBoxRounded(settings.boundingBoxRounded);
        bridge->setBoundingBoxRounding(settings.boundingBoxRounding);
        bridge->setBoundingBoxType(settings.boundingBoxType);
        bridge->setItemESPToggleKey(settings.itemESPToggleKey);
        bridge->setItemESPEnabled(settings.itemESPEnabled);

        // Apply skeleton ESP settings (DayZ2-style)
        bridge->setPlayerSkeletonEnabled(settings.playerSkeletonEnabled);
        bridge->setZombieSkeletonEnabled(settings.zombieSkeletonEnabled);
        bridge->setSkeletonLineThickness(settings.skeletonLineThickness);
        bridge->setPlayerSkeletonMaxDistance(settings.playerSkeletonMaxDistance);
        bridge->setZombieSkeletonMaxDistance(settings.zombieSkeletonMaxDistance);
        bridge->setSkeletonDetailLevel(settings.skeletonDetailLevel);

        // Apply health bar settings
        bridge->setPlayerHealthBarEnabled(settings.playerHealthBarEnabled);
        bridge->setZombieHealthBarEnabled(settings.zombieHealthBarEnabled);
        bridge->setHealthBarWidth(settings.healthBarWidth);
        bridge->setHealthBarHeight(settings.healthBarHeight);
        bridge->setHealthBarOffsetY(settings.healthBarOffsetY);
        bridge->setHealthBarMaxDistance(settings.healthBarMaxDistance);
        bridge->setHealthBarType(settings.healthBarType);
        bridge->setShowHealthNumbers(settings.showHealthNumbers);
        bridge->setShowHealthBar(settings.showHealthBar);
        bridge->setShowAllHealthStats(settings.showAllHealthStats);

        // Apply personal health display settings
        bridge->setShowPersonalHealth(settings.showPersonalHealth);
        bridge->setPersonalHealthPosition(settings.personalHealthPosition);
        bridge->setPersonalHealthScale(settings.personalHealthScale);
        bridge->setPersonalHealthBackground(settings.personalHealthBackground);
        bridge->setUseIndividualPositioning(settings.useIndividualPositioning);
        bridge->setHealthIndicatorX(settings.healthIndicatorX);
        bridge->setHealthIndicatorY(settings.healthIndicatorY);
        bridge->setBloodIndicatorX(settings.bloodIndicatorX);
        bridge->setBloodIndicatorY(settings.bloodIndicatorY);
        bridge->setShockIndicatorX(settings.shockIndicatorX);
        bridge->setShockIndicatorY(settings.shockIndicatorY);
        bridge->setStatusIndicatorX(settings.statusIndicatorX);
        bridge->setStatusIndicatorY(settings.statusIndicatorY);
        bridge->setShowHealthIndicator(settings.showHealthIndicator);
        bridge->setShowBloodIndicator(settings.showBloodIndicator);
        bridge->setShowShockIndicator(settings.showShockIndicator);
        bridge->setShowStatusIndicator(settings.showStatusIndicator);
        bridge->setShowHealthLabels(settings.showHealthLabels);
        bridge->setUseCustomFont(settings.useCustomFont);

        // Apply crosshair settings
        bridge->setCrosshairEnabled(settings.crosshairEnabled);
        bridge->setCrosshairSize(settings.crosshairSize);
        bridge->setCrosshairThickness(settings.crosshairThickness);
        bridge->setCrosshairGap(settings.crosshairGap);
        bridge->setCrosshairAlpha(settings.crosshairAlpha);
        bridge->setCrosshairOutline(settings.crosshairOutline);
        bridge->setCrosshairOutlineThickness(settings.crosshairOutlineThickness);
        bridge->setCrosshairDot(settings.crosshairDot);
        bridge->setCrosshairDotSize(settings.crosshairDotSize);
        bridge->setCrosshairUseAimPoint(settings.crosshairUseAimPoint);
        bridge->setCrosshairVerticalOffset(settings.crosshairVerticalOffset);
        bridge->setCrosshairColorR(settings.crosshairColorR);
        bridge->setCrosshairColorG(settings.crosshairColorG);
        bridge->setCrosshairColorB(settings.crosshairColorB);
        bridge->setCrosshairColorA(settings.crosshairColorA);
        bridge->setCrosshairOutlineColorR(settings.crosshairOutlineColorR);
        bridge->setCrosshairOutlineColorG(settings.crosshairOutlineColorG);
        bridge->setCrosshairOutlineColorB(settings.crosshairOutlineColorB);
        bridge->setCrosshairOutlineColorA(settings.crosshairOutlineColorA);

        // Apply container contents settings
        bridge->setShowContainerContents(settings.showContainerContents);
        bridge->setContainerContentsMaxItems(settings.containerContentsMaxItems);
        bridge->setContainerContentsMaxDistance(settings.containerContentsMaxDistance);

        // Apply font size settings
        bridge->setPlayerNameFontSize(settings.playerNameFontSize);
        bridge->setItemFontSize(settings.itemFontSize);
        bridge->setDistanceFontSize(settings.distanceFontSize);

        // Apply Multi-Core settings to global config
        if (settings.multiCoreProfile >= 0 && settings.multiCoreProfile <= 4) {
            DayZ::g_multiCoreConfig.setProfile(static_cast<DayZ::MultiCoreConfig::ProfileType>(settings.multiCoreProfile));
        }
        DayZ::g_multiCoreConfig.enableParallelProcessing = settings.enableParallelProcessing;
        DayZ::g_multiCoreConfig.enableThreadAffinity = settings.enableThreadAffinity;
        DayZ::g_multiCoreConfig.adaptiveTimings = settings.adaptiveTimings;
        DayZ::g_multiCoreConfig.maxWorkerThreads = settings.maxWorkerThreads;
    }

    void SettingsManager::loadFromRenderBridge(RenderBridge* bridge) {
        if (!bridge) return;

        // Load radar settings
        settings.showDeadPlayers = bridge->shouldShowDeadPlayers();
        settings.showVehicles = bridge->shouldShowVehicles();
        settings.showGrounditems = bridge->shouldShowGrounditems();
        settings.showBoats = bridge->shouldShowBoats();
        settings.showDeadAnimals = bridge->shouldShowDeadAnimals();
        settings.showClothing = bridge->shouldShowClothing();
        settings.showWeapons = bridge->shouldShowWeapons();
        settings.showProxyMagazines = bridge->shouldShowProxyMagazines();
        settings.showBackpacks = bridge->shouldShowBackpacks();
        settings.showRare = bridge->shouldShowRare();
        settings.showFood = bridge->shouldShowFood();
        settings.showAmmo = bridge->shouldShowAmmo();
        settings.showPlayerList = bridge->shouldShowPlayerList();
        settings.showServerPlayerList = bridge->shouldShowServerPlayerList();
        settings.showZombies = bridge->shouldShowZombies();
        settings.showAnimals = bridge->shouldShowAnimals();
        settings.playername = bridge->shouldPlayerName();
        settings.BlipSize = bridge->shouldBlipSize();
        settings.BlipSize2 = bridge->shouldBlipSize2();
        settings.Aimlinelength = bridge->shouldAimlinelength();
        settings.showOptics = bridge->shouldShowOptics();
        settings.showBase = bridge->shouldShowBase();
        settings.showMelee = bridge->shouldShowMelee();
        settings.showContainer = bridge->shouldShowContainer();
        settings.showCooking = bridge->shouldShowCooking();
        settings.showCamping = bridge->shouldShowCamping();
        settings.showStash = bridge->shouldShowStash();
        settings.LootDistanceDeadzone = bridge->shouldLootDistanceDeadzone();
        settings.showExplosives = bridge->shouldShowExplosives();
        settings.ZombiesBlipSize = bridge->shouldZombiesBlipSize();
        settings.AnimalsBlipSize = bridge->shouldAnimalsBlipSize();
        settings.FontRADAR = bridge->shouldRadarFont();
        settings.FontRADAR2 = bridge->shouldRadarFont2();
        settings.showPlayerNameRadar = bridge->shouldShowPlayerNameRadar();
        settings.showPlayerDistanceRadar = bridge->shouldShowPlayerDistanceRadar();
        settings.showPlayerHandRadar = bridge->shouldShowPlayerHandRadar();

        // Load ESP settings
        settings.showDeadPlayersFUSER = bridge->shouldShowDeadPlayersFUSER();
        settings.showVehiclesFUSER = bridge->shouldShowVehiclesFUSER();
        settings.showGrounditemsFUSER = bridge->shouldShowGrounditemsFUSER();
        settings.showBoatsFUSER = bridge->shouldShowBoatsFUSER();
        settings.showDeadAnimalsFUSER = bridge->shouldShowDeadAnimalsFUSER();
        settings.showClothingFUSER = bridge->shouldShowClothingFUSER();
        settings.showWeaponsFUSER = bridge->shouldShowWeaponsFUSER();
        settings.showProxyMagazinesFUSER = bridge->shouldShowProxyMagazinesFUSER();
        settings.showBackpacksFUSER = bridge->shouldShowBackpacksFUSER();
        settings.showRareFUSER = bridge->shouldShowRareFUSER();
        settings.showFoodFUSER = bridge->shouldShowFoodFUSER();
        settings.showAmmoFUSER = bridge->shouldShowAmmoFUSER();
        settings.showZombiesFUSER = bridge->shouldShowZombiesFUSER();
        settings.showAnimalsFUSER = bridge->shouldShowAnimalsFUSER();
        settings.lootDistanceFUSER = bridge->shouldlootDistanceFUSER();
        settings.ZombieDistanceFUSER = bridge->shouldZombieDistanceFUSER();
        settings.showPlayerInfoesp = bridge->shouldShowPlayerInfoesp();
        settings.showPlayerNameFUSER = bridge->shouldShowPlayerNameFUSER();
        settings.showPlayerDistanceFUSER = bridge->shouldShowPlayerDistanceFUSER();
        settings.showPlayerHandFUSER = bridge->shouldShowPlayerHandFUSER();
        settings.showPlayerBoxFUSER = bridge->shouldShowPlayerBoxFUSER();
        settings.showAnimalBoxFUSER = bridge->shouldShowAnimalBoxFUSER();
        settings.showAnimalNameFUSER = bridge->shouldShowAnimalNameFUSER();
        settings.showZombieBoxFUSER = bridge->shouldShowZombieBoxFUSER();
        settings.showZombieNameFUSER = bridge->shouldShowZombieNameFUSER();
        settings.showZombieDistanceFUSER = bridge->shouldShowZombieDistanceFUSER();
        settings.showOpticsFUSER = bridge->shouldShowOpticsFUSER();
        settings.showDebugFUSER = bridge->shouldShowDebugFUSER();

        // Load entity distance settings
        settings.animalMaxDistance = bridge->getAnimalMaxDistance();
        settings.vehicleMaxDistance = bridge->getVehicleMaxDistance();
        settings.boatMaxDistance = bridge->getBoatMaxDistance();
        settings.deadPlayerMaxDistance = bridge->getDeadPlayerMaxDistance();
        settings.deadAnimalMaxDistance = bridge->getDeadAnimalMaxDistance();
        settings.playerMaxDistance = bridge->getPlayerMaxDistance();
        settings.showBaseFUSER = bridge->shouldShowBaseFUSER();
        settings.showMeleeFUSER = bridge->shouldShowMeleeFUSER();
        settings.showExplosivesFUSER = bridge->shouldShowExplosivesFUSER();
        settings.showContainerFUSER = bridge->shouldShowContainerFUSER();
        settings.showCookingFUSER = bridge->shouldShowCookingFUSER();
        settings.showCampingFUSER = bridge->shouldShowCampingFUSER();
        settings.showStashFUSER = bridge->shouldShowStashFUSER();
        settings.LootDebugDistance = bridge->shouldLootDebugDistance();
        settings.enableESPDebugLogging = bridge->getEnableESPDebugLogging();
        settings.espTextSize = bridge->getESPTextSize();
        settings.baseFontSize = bridge->getBaseFontSize();
        settings.selectedFontIndex = bridge->getSelectedFontIndex();
        settings.fontBold = bridge->getFontBold();
        settings.fontOutline = bridge->getFontOutline();
        settings.fontShadow = bridge->getFontShadow();
        settings.fontAlpha = bridge->getFontAlpha();
        settings.fontShadowOffset = bridge->getFontShadowOffset();
        settings.fontOutlineSize = bridge->getFontOutlineSize();
        settings.fontOversampleH = bridge->getFontOversampleH();
        settings.fontOversampleV = bridge->getFontOversampleV();
        settings.fontRasterizerMultiply = bridge->getFontRasterizerMultiply();
        settings.fontPixelSnap = bridge->getFontPixelSnap();
        settings.fontAtlasSize = bridge->getFontAtlasSize();
        settings.fpsCounterFontSize = bridge->getFpsCounterFontSize();
        settings.fpsCounterHorizontalPadding = bridge->getFpsCounterHorizontalPadding();
        settings.fpsCounterVerticalPadding = bridge->getFpsCounterVerticalPadding();
        settings.boundingBoxThickness = bridge->getBoundingBoxThickness();
        settings.boundingBoxAntiAliasing = bridge->getBoundingBoxAntiAliasing();
        settings.boundingBoxRounded = bridge->getBoundingBoxRounded();
        settings.boundingBoxRounding = bridge->getBoundingBoxRounding();
        settings.boundingBoxType = bridge->getBoundingBoxType();
        settings.itemESPToggleKey = bridge->getItemESPToggleKey();
        settings.itemESPEnabled = bridge->isItemESPEnabled();

        // Load skeleton ESP settings (DayZ2-style)
        settings.playerSkeletonEnabled = bridge->isPlayerSkeletonEnabled();
        settings.zombieSkeletonEnabled = bridge->isZombieSkeletonEnabled();
        settings.skeletonLineThickness = bridge->getSkeletonLineThickness();
        settings.playerSkeletonMaxDistance = bridge->getPlayerSkeletonMaxDistance();
        settings.zombieSkeletonMaxDistance = bridge->getZombieSkeletonMaxDistance();
        settings.skeletonDetailLevel = bridge->getSkeletonDetailLevel();

        // Load health bar settings
        settings.playerHealthBarEnabled = bridge->isPlayerHealthBarEnabled();
        settings.zombieHealthBarEnabled = bridge->isZombieHealthBarEnabled();
        settings.healthBarWidth = bridge->getHealthBarWidth();
        settings.healthBarHeight = bridge->getHealthBarHeight();
        settings.healthBarOffsetY = bridge->getHealthBarOffsetY();
        settings.healthBarMaxDistance = bridge->getHealthBarMaxDistance();
        settings.healthBarType = bridge->getHealthBarType();
        settings.showHealthNumbers = bridge->isShowHealthNumbers();
        settings.showHealthBar = bridge->isShowHealthBar();
        settings.showAllHealthStats = bridge->isShowAllHealthStats();

        // Load personal health display settings
        settings.showPersonalHealth = bridge->isShowPersonalHealth();
        settings.personalHealthPosition = bridge->getPersonalHealthPosition();
        settings.personalHealthScale = bridge->getPersonalHealthScale();
        settings.personalHealthBackground = bridge->isPersonalHealthBackground();
        settings.useIndividualPositioning = bridge->isUseIndividualPositioning();
        settings.healthIndicatorX = bridge->getHealthIndicatorX();
        settings.healthIndicatorY = bridge->getHealthIndicatorY();
        settings.bloodIndicatorX = bridge->getBloodIndicatorX();
        settings.bloodIndicatorY = bridge->getBloodIndicatorY();
        settings.shockIndicatorX = bridge->getShockIndicatorX();
        settings.shockIndicatorY = bridge->getShockIndicatorY();
        settings.statusIndicatorX = bridge->getStatusIndicatorX();
        settings.statusIndicatorY = bridge->getStatusIndicatorY();
        settings.showHealthIndicator = bridge->isShowHealthIndicator();
        settings.showBloodIndicator = bridge->isShowBloodIndicator();
        settings.showShockIndicator = bridge->isShowShockIndicator();
        settings.showStatusIndicator = bridge->isShowStatusIndicator();
        settings.showHealthLabels = bridge->isShowHealthLabels();
        settings.useCustomFont = bridge->isUseCustomFont();

        // Load excluded players list
        settings.excludedPlayerSteamIDs = bridge->getExcludedPlayers();

        // Load crosshair settings
        settings.crosshairEnabled = bridge->isCrosshairEnabled();
        settings.crosshairSize = bridge->getCrosshairSize();
        settings.crosshairThickness = bridge->getCrosshairThickness();
        settings.crosshairGap = bridge->getCrosshairGap();
        settings.crosshairAlpha = bridge->getCrosshairAlpha();
        settings.crosshairOutline = bridge->isCrosshairOutline();
        settings.crosshairOutlineThickness = bridge->getCrosshairOutlineThickness();
        settings.crosshairDot = bridge->isCrosshairDot();
        settings.crosshairDotSize = bridge->getCrosshairDotSize();
        settings.crosshairUseAimPoint = bridge->isCrosshairUseAimPoint();
        settings.crosshairVerticalOffset = bridge->getCrosshairVerticalOffset();
        settings.crosshairColorR = bridge->getCrosshairColorR();
        settings.crosshairColorG = bridge->getCrosshairColorG();
        settings.crosshairColorB = bridge->getCrosshairColorB();
        settings.crosshairColorA = bridge->getCrosshairColorA();
        settings.crosshairOutlineColorR = bridge->getCrosshairOutlineColorR();
        settings.crosshairOutlineColorG = bridge->getCrosshairOutlineColorG();
        settings.crosshairOutlineColorB = bridge->getCrosshairOutlineColorB();
        settings.crosshairOutlineColorA = bridge->getCrosshairOutlineColorA();

        // Load container contents settings
        settings.showContainerContents = bridge->shouldShowContainerContents();
        settings.containerContentsMaxItems = bridge->getContainerContentsMaxItems();
        settings.containerContentsMaxDistance = bridge->getContainerContentsMaxDistance();

        // Load font size settings
        settings.playerNameFontSize = bridge->getPlayerNameFontSize();
        settings.itemFontSize = bridge->getItemFontSize();
        settings.distanceFontSize = bridge->getDistanceFontSize();

        // Load Multi-Core settings from global config
        settings.multiCoreProfile = static_cast<int>(DayZ::g_multiCoreConfig.getCurrentProfileType());
        settings.enableParallelProcessing = DayZ::g_multiCoreConfig.enableParallelProcessing;
        settings.enableThreadAffinity = DayZ::g_multiCoreConfig.enableThreadAffinity;
        settings.adaptiveTimings = DayZ::g_multiCoreConfig.adaptiveTimings;
        settings.maxWorkerThreads = DayZ::g_multiCoreConfig.maxWorkerThreads;
    }

    // Helper function to parse ESP color settings (avoids compiler nesting limit)
    void SettingsManager::parseESPAndOtherSettings(const std::string& key, const std::string& value, AppSettings& settings) {
        // Helper functions to convert string values
        auto toBool = [](const std::string& str) { return str == "1" || str == "true"; };
        auto toInt = [](const std::string& str) { return std::stoi(str); };
        auto toFloat = [](const std::string& str) { return std::stof(str); };

        // ESP settings
        if (key == "showDeadPlayersFUSER") settings.showDeadPlayersFUSER = toBool(value);
        else if (key == "showVehiclesFUSER") settings.showVehiclesFUSER = toBool(value);
        else if (key == "showGrounditemsFUSER") settings.showGrounditemsFUSER = toBool(value);
        else if (key == "showBoatsFUSER") settings.showBoatsFUSER = toBool(value);
        else if (key == "showDeadAnimalsFUSER") settings.showDeadAnimalsFUSER = toBool(value);
        else if (key == "showClothingFUSER") settings.showClothingFUSER = toBool(value);
        else if (key == "showWeaponsFUSER") settings.showWeaponsFUSER = toBool(value);
        else if (key == "showProxyMagazinesFUSER") settings.showProxyMagazinesFUSER = toBool(value);
        else if (key == "showBackpacksFUSER") settings.showBackpacksFUSER = toBool(value);
        else if (key == "showRareFUSER") settings.showRareFUSER = toBool(value);
        else if (key == "showFoodFUSER") settings.showFoodFUSER = toBool(value);
        else if (key == "showAmmoFUSER") settings.showAmmoFUSER = toBool(value);
        else if (key == "showZombiesFUSER") settings.showZombiesFUSER = toBool(value);
        else if (key == "showAnimalsFUSER") settings.showAnimalsFUSER = toBool(value);
        else if (key == "lootDistanceFUSER") settings.lootDistanceFUSER = toInt(value);
        else if (key == "ZombieDistanceFUSER") settings.ZombieDistanceFUSER = toInt(value);
        else if (key == "showPlayerInfoesp") settings.showPlayerInfoesp = toBool(value);
        else if (key == "showPlayerNameFUSER") settings.showPlayerNameFUSER = toBool(value);
        else if (key == "showPlayerDistanceFUSER") settings.showPlayerDistanceFUSER = toBool(value);
        else if (key == "showPlayerHandFUSER") settings.showPlayerHandFUSER = toBool(value);
        else if (key == "showPlayerBoxFUSER") settings.showPlayerBoxFUSER = toBool(value);
        else if (key == "showAnimalBoxFUSER") settings.showAnimalBoxFUSER = toBool(value);
        else if (key == "showZombieBoxFUSER") settings.showZombieBoxFUSER = toBool(value);
        else if (key == "showZombieNameFUSER") settings.showZombieNameFUSER = toBool(value);
        else if (key == "showZombieDistanceFUSER") settings.showZombieDistanceFUSER = toBool(value);
        else if (key == "showOpticsFUSER") settings.showOpticsFUSER = toBool(value);
        else if (key == "showDebugFUSER") settings.showDebugFUSER = toBool(value);
        else if (key == "showBaseFUSER") settings.showBaseFUSER = toBool(value);
        else if (key == "showMeleeFUSER") settings.showMeleeFUSER = toBool(value);
        else if (key == "showExplosivesFUSER") settings.showExplosivesFUSER = toBool(value);
        else if (key == "showContainerFUSER") settings.showContainerFUSER = toBool(value);
        else if (key == "showCookingFUSER") settings.showCookingFUSER = toBool(value);
        else if (key == "showCampingFUSER") settings.showCampingFUSER = toBool(value);
        else if (key == "showStashFUSER") settings.showStashFUSER = toBool(value);
        else if (key == "LootDebugDistance") settings.LootDebugDistance = toInt(value);
        else if (key == "enableESPDebugLogging") settings.enableESPDebugLogging = toBool(value);
        else if (key == "espTextSize") settings.espTextSize = toFloat(value);
        else if (key == "baseFontSize") settings.baseFontSize = toFloat(value);
        else if (key == "selectedFontIndex") settings.selectedFontIndex = toInt(value);
        else if (key == "fontBold") settings.fontBold = toBool(value);
        else if (key == "fontOutline") settings.fontOutline = toBool(value);
        else if (key == "fontShadow") settings.fontShadow = toBool(value);
        else if (key == "fontAlpha") settings.fontAlpha = toFloat(value);
        else if (key == "fontShadowOffset") settings.fontShadowOffset = toFloat(value);
        else if (key == "fontOutlineSize") settings.fontOutlineSize = toFloat(value);
        else if (key == "fontOversampleH") settings.fontOversampleH = toInt(value);
        else if (key == "fontOversampleV") settings.fontOversampleV = toInt(value);
        else if (key == "fontRasterizerMultiply") settings.fontRasterizerMultiply = toFloat(value);
        else if (key == "fontPixelSnap") settings.fontPixelSnap = toBool(value);
        else if (key == "fontAtlasSize") settings.fontAtlasSize = toInt(value);
        else if (key == "fontUIBias") settings.fontUIBias = toFloat(value);
        else if (key == "fpsCounterFontSize") settings.fpsCounterFontSize = toFloat(value);
        else if (key == "fpsCounterHorizontalPadding") settings.fpsCounterHorizontalPadding = toFloat(value);
        else if (key == "fpsCounterVerticalPadding") settings.fpsCounterVerticalPadding = toFloat(value);
        else if (key == "boundingBoxThickness") settings.boundingBoxThickness = toFloat(value);
        else if (key == "boundingBoxAntiAliasing") settings.boundingBoxAntiAliasing = toBool(value);
        else if (key == "boundingBoxRounded") settings.boundingBoxRounded = toBool(value);
        else if (key == "boundingBoxRounding") settings.boundingBoxRounding = toFloat(value);
        else if (key == "itemESPToggleKey") settings.itemESPToggleKey = toInt(value);
        else if (key == "itemESPEnabled") settings.itemESPEnabled = toBool(value);

        // UI settings
        else if (key == "followPlayerEnabled") settings.followPlayerEnabled = toBool(value);

        // Player exclusion settings
        else if (key == "excludedPlayerCount") {
            // Clear existing list and prepare for loading
            settings.excludedPlayerSteamIDs.clear();
            fflush(stdout);
        }
        else if (key.substr(0, 14) == "excludedPlayer") {
            // Load individual excluded player Steam ID
            settings.excludedPlayerSteamIDs.push_back(value);
            fflush(stdout);
        }

        // Performance settings
        else if (key == "vSyncEnabled") settings.vSyncEnabled = toBool(value);

        // Multi-Core settings
        else if (key == "multiCoreProfile") settings.multiCoreProfile = toInt(value);
        else if (key == "enableParallelProcessing") settings.enableParallelProcessing = toBool(value);
        else if (key == "enableThreadAffinity") settings.enableThreadAffinity = toBool(value);
        else if (key == "adaptiveTimings") settings.adaptiveTimings = toBool(value);
        else if (key == "maxWorkerThreads") settings.maxWorkerThreads = toInt(value);

        // Skeleton ESP settings (DayZ2-style)
        else if (key == "playerSkeletonEnabled") settings.playerSkeletonEnabled = toBool(value);
        else if (key == "zombieSkeletonEnabled") settings.zombieSkeletonEnabled = toBool(value);
        else if (key == "skeletonLineThickness") settings.skeletonLineThickness = toFloat(value);
        else if (key == "playerSkeletonMaxDistance") settings.playerSkeletonMaxDistance = toFloat(value);
        else if (key == "zombieSkeletonMaxDistance") settings.zombieSkeletonMaxDistance = toFloat(value);
        else if (key == "skeletonDetailLevel") settings.skeletonDetailLevel = toInt(value);

        // Health Bar ESP settings
        else if (key == "playerHealthBarEnabled") settings.playerHealthBarEnabled = toBool(value);
        else if (key == "zombieHealthBarEnabled") settings.zombieHealthBarEnabled = toBool(value);
        else if (key == "healthBarWidth") settings.healthBarWidth = toFloat(value);
        else if (key == "healthBarHeight") settings.healthBarHeight = toFloat(value);
        else if (key == "healthBarOffsetY") settings.healthBarOffsetY = toFloat(value);
        else if (key == "healthBarMaxDistance") settings.healthBarMaxDistance = toFloat(value);
        else if (key == "healthBarType") settings.healthBarType = toInt(value);
        else if (key == "showHealthNumbers") settings.showHealthNumbers = toBool(value);
        else if (key == "showHealthBar") settings.showHealthBar = toBool(value);
        else if (key == "showAllHealthStats") settings.showAllHealthStats = toBool(value);

        // Personal Health Display settings
        else if (key == "showPersonalHealth") settings.showPersonalHealth = toBool(value);
        else if (key == "personalHealthPosition") settings.personalHealthPosition = toInt(value);
        else if (key == "personalHealthScale") settings.personalHealthScale = toFloat(value);
        else if (key == "personalHealthBackground") settings.personalHealthBackground = toBool(value);
        else if (key == "useIndividualPositioning") settings.useIndividualPositioning = toBool(value);
        else if (key == "healthIndicatorX") settings.healthIndicatorX = toFloat(value);
        else if (key == "healthIndicatorY") settings.healthIndicatorY = toFloat(value);
        else if (key == "bloodIndicatorX") settings.bloodIndicatorX = toFloat(value);
        else if (key == "bloodIndicatorY") settings.bloodIndicatorY = toFloat(value);
        else if (key == "shockIndicatorX") settings.shockIndicatorX = toFloat(value);
        else if (key == "shockIndicatorY") settings.shockIndicatorY = toFloat(value);
        else if (key == "statusIndicatorX") settings.statusIndicatorX = toFloat(value);
        else if (key == "statusIndicatorY") settings.statusIndicatorY = toFloat(value);
        else if (key == "showHealthIndicator") settings.showHealthIndicator = toBool(value);
        else if (key == "showBloodIndicator") settings.showBloodIndicator = toBool(value);
        else if (key == "showShockIndicator") settings.showShockIndicator = toBool(value);
        else if (key == "showStatusIndicator") settings.showStatusIndicator = toBool(value);
        else if (key == "showHealthLabels") settings.showHealthLabels = toBool(value);
        else if (key == "useCustomFont") settings.useCustomFont = toBool(value);

        // ESP Color settings
        // Player Colors
        if (key == "playerBoxColorR") settings.playerBoxColorR = toInt(value);
        else if (key == "playerBoxColorG") settings.playerBoxColorG = toInt(value);
        else if (key == "playerBoxColorB") settings.playerBoxColorB = toInt(value);
        else if (key == "playerBoxColorA") settings.playerBoxColorA = toInt(value);
        else if (key == "playerTextColorR") settings.playerTextColorR = toInt(value);
        else if (key == "playerTextColorG") settings.playerTextColorG = toInt(value);
        else if (key == "playerTextColorB") settings.playerTextColorB = toInt(value);
        else if (key == "playerTextColorA") settings.playerTextColorA = toInt(value);
        // Individual Player Text Colors
        else if (key == "playerNameColorR") settings.playerNameColorR = toInt(value);
        else if (key == "playerNameColorG") settings.playerNameColorG = toInt(value);
        else if (key == "playerNameColorB") settings.playerNameColorB = toInt(value);
        else if (key == "playerNameColorA") settings.playerNameColorA = toInt(value);
        else if (key == "playerDistanceColorR") settings.playerDistanceColorR = toInt(value);
        else if (key == "playerDistanceColorG") settings.playerDistanceColorG = toInt(value);
        else if (key == "playerDistanceColorB") settings.playerDistanceColorB = toInt(value);
        else if (key == "playerDistanceColorA") settings.playerDistanceColorA = toInt(value);
        else if (key == "playerHandColorR") settings.playerHandColorR = toInt(value);
        else if (key == "playerHandColorG") settings.playerHandColorG = toInt(value);
        else if (key == "playerHandColorB") settings.playerHandColorB = toInt(value);
        else if (key == "playerHandColorA") settings.playerHandColorA = toInt(value);
        else if (key == "playerSkeletonColorR") settings.playerSkeletonColorR = toInt(value);
        else if (key == "playerSkeletonColorG") settings.playerSkeletonColorG = toInt(value);
        else if (key == "playerSkeletonColorB") settings.playerSkeletonColorB = toInt(value);
        else if (key == "playerSkeletonColorA") settings.playerSkeletonColorA = toInt(value);

        // Zombie Colors
        else if (key == "zombieBoxColorR") settings.zombieBoxColorR = toInt(value);
        else if (key == "zombieBoxColorG") settings.zombieBoxColorG = toInt(value);
        else if (key == "zombieBoxColorB") settings.zombieBoxColorB = toInt(value);
        else if (key == "zombieBoxColorA") settings.zombieBoxColorA = toInt(value);
        else if (key == "zombieTextColorR") settings.zombieTextColorR = toInt(value);
        else if (key == "zombieTextColorG") settings.zombieTextColorG = toInt(value);
        else if (key == "zombieTextColorB") settings.zombieTextColorB = toInt(value);
        else if (key == "zombieTextColorA") settings.zombieTextColorA = toInt(value);
        else if (key == "zombieSkeletonColorR") settings.zombieSkeletonColorR = toInt(value);
        else if (key == "zombieSkeletonColorG") settings.zombieSkeletonColorG = toInt(value);
        else if (key == "zombieSkeletonColorB") settings.zombieSkeletonColorB = toInt(value);
        else if (key == "zombieSkeletonColorA") settings.zombieSkeletonColorA = toInt(value);

        // Animal Colors
        else if (key == "animalBoxColorR") settings.animalBoxColorR = toInt(value);
        else if (key == "animalBoxColorG") settings.animalBoxColorG = toInt(value);
        else if (key == "animalBoxColorB") settings.animalBoxColorB = toInt(value);
        else if (key == "animalBoxColorA") settings.animalBoxColorA = toInt(value);
        else if (key == "animalTextColorR") settings.animalTextColorR = toInt(value);
        else if (key == "animalTextColorG") settings.animalTextColorG = toInt(value);
        else if (key == "animalTextColorB") settings.animalTextColorB = toInt(value);
        else if (key == "animalTextColorA") settings.animalTextColorA = toInt(value);

        // Dead Player Colors
        else if (key == "deadPlayerBoxColorR") settings.deadPlayerBoxColorR = toInt(value);
        else if (key == "deadPlayerBoxColorG") settings.deadPlayerBoxColorG = toInt(value);
        else if (key == "deadPlayerBoxColorB") settings.deadPlayerBoxColorB = toInt(value);
        else if (key == "deadPlayerBoxColorA") settings.deadPlayerBoxColorA = toInt(value);
        else if (key == "deadPlayerTextColorR") settings.deadPlayerTextColorR = toInt(value);
        else if (key == "deadPlayerTextColorG") settings.deadPlayerTextColorG = toInt(value);
        else if (key == "deadPlayerTextColorB") settings.deadPlayerTextColorB = toInt(value);
        else if (key == "deadPlayerTextColorA") settings.deadPlayerTextColorA = toInt(value);

        // Vehicle Colors
        else if (key == "vehicleBoxColorR") settings.vehicleBoxColorR = toInt(value);
        else if (key == "vehicleBoxColorG") settings.vehicleBoxColorG = toInt(value);
        else if (key == "vehicleBoxColorB") settings.vehicleBoxColorB = toInt(value);
        else if (key == "vehicleBoxColorA") settings.vehicleBoxColorA = toInt(value);
        else if (key == "vehicleTextColorR") settings.vehicleTextColorR = toInt(value);
        else if (key == "vehicleTextColorG") settings.vehicleTextColorG = toInt(value);
        else if (key == "vehicleTextColorB") settings.vehicleTextColorB = toInt(value);
        else if (key == "vehicleTextColorA") settings.vehicleTextColorA = toInt(value);

        // Health Bar Colors
        else if (key == "healthBarHighColorR") settings.healthBarHighColorR = toInt(value);
        else if (key == "healthBarHighColorG") settings.healthBarHighColorG = toInt(value);
        else if (key == "healthBarHighColorB") settings.healthBarHighColorB = toInt(value);
        else if (key == "healthBarHighColorA") settings.healthBarHighColorA = toInt(value);
        else if (key == "healthBarMediumColorR") settings.healthBarMediumColorR = toInt(value);
        else if (key == "healthBarMediumColorG") settings.healthBarMediumColorG = toInt(value);
        else if (key == "healthBarMediumColorB") settings.healthBarMediumColorB = toInt(value);
        else if (key == "healthBarMediumColorA") settings.healthBarMediumColorA = toInt(value);
        else if (key == "healthBarLowColorR") settings.healthBarLowColorR = toInt(value);
        else if (key == "healthBarLowColorG") settings.healthBarLowColorG = toInt(value);
        else if (key == "healthBarLowColorB") settings.healthBarLowColorB = toInt(value);
        else if (key == "healthBarLowColorA") settings.healthBarLowColorA = toInt(value);
        else if (key == "healthBarBackgroundColorR") settings.healthBarBackgroundColorR = toInt(value);
        else if (key == "healthBarBackgroundColorG") settings.healthBarBackgroundColorG = toInt(value);
        else if (key == "healthBarBackgroundColorB") settings.healthBarBackgroundColorB = toInt(value);
        else if (key == "healthBarBackgroundColorA") settings.healthBarBackgroundColorA = toInt(value);

        // Container Contents Colors
        else if (key == "containerContentsColorR") settings.containerContentsColorR = toInt(value);
        else if (key == "containerContentsColorG") settings.containerContentsColorG = toInt(value);
        else if (key == "containerContentsColorB") settings.containerContentsColorB = toInt(value);
        else if (key == "containerContentsColorA") settings.containerContentsColorA = toInt(value);

        // Entity distance settings
        else if (key == "animalMaxDistance") settings.animalMaxDistance = toInt(value);
        else if (key == "vehicleMaxDistance") settings.vehicleMaxDistance = toInt(value);
        else if (key == "boatMaxDistance") settings.boatMaxDistance = toInt(value);
        else if (key == "deadPlayerMaxDistance") settings.deadPlayerMaxDistance = toInt(value);
        else if (key == "deadAnimalMaxDistance") settings.deadAnimalMaxDistance = toInt(value);
        else if (key == "playerMaxDistance") settings.playerMaxDistance = toInt(value);
        else if (key == "showAnimalNameFUSER") settings.showAnimalNameFUSER = toBool(value);

        // Crosshair settings
        else if (key == "crosshairEnabled") settings.crosshairEnabled = toBool(value);
        else if (key == "crosshairSize") settings.crosshairSize = toFloat(value);
        else if (key == "crosshairThickness") settings.crosshairThickness = toFloat(value);
        else if (key == "crosshairGap") settings.crosshairGap = toFloat(value);
        else if (key == "crosshairAlpha") settings.crosshairAlpha = toFloat(value);
        else if (key == "crosshairOutline") settings.crosshairOutline = toBool(value);
        else if (key == "crosshairOutlineThickness") settings.crosshairOutlineThickness = toFloat(value);
        else if (key == "crosshairDot") settings.crosshairDot = toBool(value);
        else if (key == "crosshairDotSize") settings.crosshairDotSize = toFloat(value);
        else if (key == "crosshairUseAimPoint") settings.crosshairUseAimPoint = toBool(value);
        else if (key == "crosshairVerticalOffset") settings.crosshairVerticalOffset = toFloat(value);
        else if (key == "crosshairColorR") settings.crosshairColorR = toInt(value);
        else if (key == "crosshairColorG") settings.crosshairColorG = toInt(value);
        else if (key == "crosshairColorB") settings.crosshairColorB = toInt(value);
        else if (key == "crosshairColorA") settings.crosshairColorA = toInt(value);
        else if (key == "crosshairOutlineColorR") settings.crosshairOutlineColorR = toInt(value);
        else if (key == "crosshairOutlineColorG") settings.crosshairOutlineColorG = toInt(value);
        else if (key == "crosshairOutlineColorB") settings.crosshairOutlineColorB = toInt(value);
        else if (key == "crosshairOutlineColorA") settings.crosshairOutlineColorA = toInt(value);

        // Container Contents settings
        else if (key == "showContainerContents") settings.showContainerContents = toBool(value);
        else if (key == "containerContentsMaxItems") settings.containerContentsMaxItems = toInt(value);
        else if (key == "containerContentsMaxDistance") settings.containerContentsMaxDistance = toFloat(value);

        // Font size settings
        else if (key == "playerNameFontSize") settings.playerNameFontSize = toFloat(value);
        else if (key == "itemFontSize") settings.itemFontSize = toFloat(value);
        else if (key == "distanceFontSize") settings.distanceFontSize = toFloat(value);
        else {
            // Try parsing loot category colors
            parseLootCategoryColors(key, value, settings);
        }
    }

    void SettingsManager::parseLootCategoryColors(const std::string& key, const std::string& value, AppSettings& settings) {
        // Helper function to convert string to int
        auto toInt = [](const std::string& str) { return std::stoi(str); };

        // Loot Category Colors
        if (key == "weaponColorR") settings.weaponColorR = toInt(value);
        else if (key == "weaponColorG") settings.weaponColorG = toInt(value);
        else if (key == "weaponColorB") settings.weaponColorB = toInt(value);
        else if (key == "weaponColorA") settings.weaponColorA = toInt(value);
        else if (key == "clothingColorR") settings.clothingColorR = toInt(value);
        else if (key == "clothingColorG") settings.clothingColorG = toInt(value);
        else if (key == "clothingColorB") settings.clothingColorB = toInt(value);
        else if (key == "clothingColorA") settings.clothingColorA = toInt(value);
        else if (key == "backpackColorR") settings.backpackColorR = toInt(value);
        else if (key == "backpackColorG") settings.backpackColorG = toInt(value);
        else if (key == "backpackColorB") settings.backpackColorB = toInt(value);
        else if (key == "backpackColorA") settings.backpackColorA = toInt(value);
        else if (key == "foodColorR") settings.foodColorR = toInt(value);
        else if (key == "foodColorG") settings.foodColorG = toInt(value);
        else if (key == "foodColorB") settings.foodColorB = toInt(value);
        else if (key == "foodColorA") settings.foodColorA = toInt(value);
        else if (key == "ammoColorR") settings.ammoColorR = toInt(value);
        else if (key == "ammoColorG") settings.ammoColorG = toInt(value);
        else if (key == "ammoColorB") settings.ammoColorB = toInt(value);
        else if (key == "ammoColorA") settings.ammoColorA = toInt(value);
        else if (key == "proxyMagazineColorR") settings.proxyMagazineColorR = toInt(value);
        else if (key == "proxyMagazineColorG") settings.proxyMagazineColorG = toInt(value);
        else if (key == "proxyMagazineColorB") settings.proxyMagazineColorB = toInt(value);
        else if (key == "proxyMagazineColorA") settings.proxyMagazineColorA = toInt(value);
        else if (key == "opticColorR") settings.opticColorR = toInt(value);
        else if (key == "opticColorG") settings.opticColorG = toInt(value);
        else if (key == "opticColorB") settings.opticColorB = toInt(value);
        else if (key == "opticColorA") settings.opticColorA = toInt(value);
        else if (key == "baseBuildingColorR") settings.baseBuildingColorR = toInt(value);
        else if (key == "baseBuildingColorG") settings.baseBuildingColorG = toInt(value);
        else if (key == "baseBuildingColorB") settings.baseBuildingColorB = toInt(value);
        else if (key == "baseBuildingColorA") settings.baseBuildingColorA = toInt(value);
        else if (key == "meleeColorR") settings.meleeColorR = toInt(value);
        else if (key == "meleeColorG") settings.meleeColorG = toInt(value);
        else if (key == "meleeColorB") settings.meleeColorB = toInt(value);
        else if (key == "meleeColorA") settings.meleeColorA = toInt(value);
        else if (key == "explosiveColorR") settings.explosiveColorR = toInt(value);
        else if (key == "explosiveColorG") settings.explosiveColorG = toInt(value);
        else if (key == "explosiveColorB") settings.explosiveColorB = toInt(value);
        else if (key == "explosiveColorA") settings.explosiveColorA = toInt(value);
        else if (key == "containerColorR") settings.containerColorR = toInt(value);
        else if (key == "containerColorG") settings.containerColorG = toInt(value);
        else if (key == "containerColorB") settings.containerColorB = toInt(value);
        else if (key == "containerColorA") settings.containerColorA = toInt(value);
        else if (key == "cookingColorR") settings.cookingColorR = toInt(value);
        else if (key == "cookingColorG") settings.cookingColorG = toInt(value);
        else if (key == "cookingColorB") settings.cookingColorB = toInt(value);
        else if (key == "cookingColorA") settings.cookingColorA = toInt(value);
        else if (key == "campingColorR") settings.campingColorR = toInt(value);
        else if (key == "campingColorG") settings.campingColorG = toInt(value);
        else if (key == "campingColorB") settings.campingColorB = toInt(value);
        else if (key == "campingColorA") settings.campingColorA = toInt(value);
        else if (key == "stashColorR") settings.stashColorR = toInt(value);
        else if (key == "stashColorG") settings.stashColorG = toInt(value);
        else if (key == "stashColorB") settings.stashColorB = toInt(value);
        else if (key == "stashColorA") settings.stashColorA = toInt(value);
        else if (key == "groundItemColorR") settings.groundItemColorR = toInt(value);
        else if (key == "groundItemColorG") settings.groundItemColorG = toInt(value);
        else if (key == "groundItemColorB") settings.groundItemColorB = toInt(value);
        else if (key == "groundItemColorA") settings.groundItemColorA = toInt(value);
        // If none of the above match, the key is not a loot category color
    }

} // namespace DMARender
