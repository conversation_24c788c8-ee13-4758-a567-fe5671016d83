#include "./VmmManager.h"

VMM_HANDLE DMAMem::VmmManager::getVmm() {
	if (!hVMM) {
		hVMM = initialize();
	}
	return hVMM;
}


VMM_HANDLE DMAMem::VmmManager::initialize()
{
	VMMDLL_CloseAll();
	std::cout << " [ + ] Connecting to DMA Card..." << std::endl;

	// Updated arguments to include -norefresh as suggested in the forum post
	LPSTR args[] = { LPSTR(""), LPSTR("-device"), LPSTR("fpga://algo=0"), LPSTR("-norefresh") };

	VMM_HANDLE handle = VMMDLL_Initialize(4, args);
	if (!handle)
		std::cout << "[ ! ] Vmm Initialization Failed..." << std::endl;

	std::cout << " [ + ] Connected to DMA Card..." << std::endl;

	// Start the manual refresh loop to prevent 5-second freezes
	// But with performance-optimized settings for ESP applications
	if (manual_refresh) {
		// Set performance mode by default to avoid ESP lag
		setPerformanceMode(true);
		startRefreshLoop();
		std::cout << " [ + ] DMA Refresh Loop Started (Performance Mode)" << std::endl;
	}

	return handle;
}

// The critical refresh loop that prevents DMA freezes every 5 seconds
void DMAMem::VmmManager::RefreshLoop() {
	uint64_t last_slow = 0;
	uint64_t last_medium = 0;
	uint64_t last_fast = 0;
	uint64_t last_mem = 0;

	while (this->hVMM != NULL) {
		if (wants_no_refresh) {
			break;
		}

		auto now = GetCurrentTime();

		// Get current configuration (atomic variables)
		uint64_t currentFastUs = fastRefreshUs.load();
		uint64_t currentMediumUs = mediumRefreshUs.load();
		uint64_t currentSlowUs = slowRefreshUs.load();
		uint64_t currentMemUs = memRefreshUs.load();
		uint64_t currentSleepMs = sleepMs.load();

		if (!last_fast) {
			last_slow = now;
			last_medium = now;
			last_fast = now;
			last_mem = now;
			continue;
		}

		// Slow refresh (configurable interval)
		if (wants_full_refresh) {
			if (now - last_slow > currentSlowUs) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_SLOW, 1);
				last_slow = GetCurrentTime();
			}
		}
		else {
			last_slow = 0;
		}

		// Medium refresh (configurable interval)
		if (wants_refresh) {
			if (now - last_medium > currentMediumUs) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_MEDIUM, 1);
				last_medium = GetCurrentTime();
			}
		}
		else {
			last_medium = 0;
		}

		// Fast refresh (configurable interval) - TLB partial refresh
		if (now - last_fast > currentFastUs) {
			VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_TLB_PARTIAL, 1);
			last_fast = GetCurrentTime();
		}

		// Memory refresh (configurable interval)
		if (wants_full_refresh) {
			if (now - last_mem > currentMemUs) {
				VMMDLL_ConfigSet(this->hVMM, VMMDLL_OPT_REFRESH_FREQ_MEM, 1);
				last_mem = GetCurrentTime();
			}
		}

		// Configurable sleep interval
		Sleep(currentSleepMs);
	}
}

void DMAMem::VmmManager::startRefreshLoop() {
	if (!refreshThreadRunning) {
		refreshThreadRunning = true;
		refreshThread = std::thread(&VmmManager::RefreshLoop, this);
		refreshThread.detach(); // Detach the thread so it runs independently
		std::cout << " [ + ] DMA Refresh Loop Started..." << std::endl;
	}
}

void DMAMem::VmmManager::stopRefreshLoop() {
	if (refreshThreadRunning) {
		wants_no_refresh = true;
		refreshThreadRunning = false;
		// Give the thread a moment to exit gracefully
		Sleep(100);
		std::cout << " [ + ] DMA Refresh Loop Stopped..." << std::endl;
	}
}

DMAMem::VmmManager::~VmmManager() {
	// Stop the refresh loop before cleanup
	stopRefreshLoop();
	
	// Close the VMM handle
	if (hVMM) {
		VMMDLL_Close(hVMM);
		hVMM = nullptr;
	}
}

BOOL DMAMem::VmmManager::readMemory(DWORD pid, QWORD remoteAddress, void* destination, int size, ULONG64 flags)
{
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, flags);
}

// Optimized memory reading for ESP performance
BOOL DMAMem::VmmManager::readMemoryOptimized(DWORD pid, QWORD remoteAddress, void* destination, int size)
{
	// Use optimized flags for better ESP performance
	ULONG64 optimizedFlags = VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_FORCECACHE_READ;
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, optimizedFlags);
}

// Ultra-fast memory reading for critical ESP operations
BOOL DMAMem::VmmManager::readMemoryUltraFast(DWORD pid, QWORD remoteAddress, void* destination, int size)
{
	// Use ultra-fast flags - no cache, no paging, force cache read
	ULONG64 ultraFlags = VMMDLL_FLAG_NOCACHE | VMMDLL_FLAG_FORCECACHE_READ | VMMDLL_FLAG_NOPAGING;
	return VMMDLL_MemReadEx(this->getVmm(), pid, remoteAddress, (PBYTE)destination, size, NULL, ultraFlags);
}

VMMDLL_SCATTER_HANDLE DMAMem::VmmManager::initializeScatter(DWORD pid)
{
	return initializeScatter(pid, VMMDLL_FLAG_NOCACHE);
}

VMMDLL_SCATTER_HANDLE DMAMem::VmmManager::initializeScatter(DWORD pid, ULONG64 flags)
{
	return VMMDLL_Scatter_Initialize(this->getVmm(), pid, flags);
}

void DMAMem::VmmManager::addScatterRead(VMMDLL_SCATTER_HANDLE handle, QWORD remoteAddress, int size, void* destination)
{
	VMMDLL_Scatter_PrepareEx(handle, remoteAddress, size, (PBYTE)destination, nullptr);
}

void DMAMem::VmmManager::executeScatter(VMMDLL_SCATTER_HANDLE handle)
{
	VMMDLL_Scatter_ExecuteRead(handle);
	VMMDLL_Scatter_CloseHandle(handle);
}

// ===== CS2-STYLE SCATTER OPTIMIZATION IMPLEMENTATIONS =====

DMAMem::VmmManager::ScatterBatch DMAMem::VmmManager::createScatterBatch(DWORD pid, ULONG64 flags)
{
	ScatterBatch batch;
	batch.handle = initializeScatter(pid, flags);
	batch.isInitialized = (batch.handle != nullptr);
	
	if (batch.isInitialized) {
		std::cout << " [ + ] CS2-Style Scatter Batch Created (PID: " << pid << ")" << std::endl;
	}
	
	return batch;
}

void DMAMem::VmmManager::addScatterBatchRead(ScatterBatch& batch, QWORD remoteAddress, int size, void* destination)
{
	if (!batch.isInitialized) {
		std::cout << "[ ! ] Cannot add to uninitialized scatter batch" << std::endl;
		return;
	}
	
	// Add to scatter handle
	addScatterRead(batch.handle, remoteAddress, size, destination);
}

bool DMAMem::VmmManager::executeScatterBatch(ScatterBatch& batch)
{
	if (!batch.isInitialized) {
		std::cout << "[ ! ] Cannot execute uninitialized scatter batch" << std::endl;
		return false;
	}
	
	// Execute all reads in a single operation (CS2-style)
	executeScatter(batch.handle);
	
	std::cout << " [ + ] CS2-Style Scatter Batch Executed" << std::endl;
	
	return true;
}

void DMAMem::VmmManager::cleanupScatterBatch(ScatterBatch& batch)
{
	if (batch.isInitialized && batch.handle) {
		VMMDLL_Scatter_CloseHandle(batch.handle);
		batch.handle = nullptr;
		batch.isInitialized = false;
		batch.readResults.clear();
	}
}


