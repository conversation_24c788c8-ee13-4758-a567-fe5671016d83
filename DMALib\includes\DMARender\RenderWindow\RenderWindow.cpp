#include "RenderWindow.h"
#include "../Settings/SettingsManager.h"
#include "../../../dma-dayz-cpp/DayZ/Config/MultiCoreConfig.h"

// FreeType integration for enhanced font rendering
#ifdef IMGUI_ENABLE_FREETYPE
#include "../../imgui/misc/freetype/imgui_freetype.h"
#endif
#include <io.h>      // For freopen_s
#include <fcntl.h>   // For console operations
#include <iostream>  // For console streams
#include <algorithm> // For std::min/std::max
#include <ctime>     // For time functions
#include <chrono>    // For time functions
#include <psapi.h>   // For memory information

// Use the global MultiCoreConfig from DayZ namespace
using DayZ::g_multiCoreConfig;

// Multi-core configuration enabled for performance tuning
#define HAS_MULTICORE_CONFIG 1

// Forward declaration for Neon Green professional theme
void ApplyNeonGreenTheme();

// Helper function to get readable key names including mouse buttons
std::string getKeyName(int keyCode) {
    switch (keyCode) {
        // Function Keys
        case 0x70: return "F1";
        case 0x71: return "F2";
        case 0x72: return "F3";
        case 0x73: return "F4";
        case 0x74: return "F5";
        case 0x75: return "F6";
        case 0x76: return "F7";
        case 0x77: return "F8";
        case 0x78: return "F9";
        case 0x79: return "F10";
        case 0x7A: return "F11";
        case 0x7B: return "F12";

        // Mouse Buttons
        case 0x01: return "Left Mouse";
        case 0x02: return "Right Mouse";
        case 0x04: return "Middle Mouse";
        case 0x05: return "Mouse 4";
        case 0x06: return "Mouse 5";

        // Keyboard Keys
        case 0x08: return "Backspace";
        case 0x09: return "Tab";
        case 0x0D: return "Enter";
        case 0x10: return "Shift";
        case 0x11: return "Ctrl";
        case 0x12: return "Alt";
        case 0x20: return "Space";
        case 0x21: return "Page Up";
        case 0x22: return "Page Down";
        case 0x23: return "End";
        case 0x24: return "Home";
        case 0x25: return "Left Arrow";
        case 0x26: return "Up Arrow";
        case 0x27: return "Right Arrow";
        case 0x28: return "Down Arrow";
        case 0x2D: return "Insert";
        case 0x2E: return "Delete";

        default:
            if (keyCode >= 0x30 && keyCode <= 0x39) { // 0-9
                return std::string(1, '0' + (keyCode - 0x30));
            }
            if (keyCode >= 0x41 && keyCode <= 0x5A) { // A-Z
                return std::string(1, 'A' + (keyCode - 0x41));
            }
            return "Key " + std::to_string(keyCode);
    }
}
#include <algorithm>

ImVec2 g_mainPlayerScreenPos = ImVec2(0, 0);
ImVec2 g_mainPlayerScreenPosBuffered = ImVec2(0, 0);

std::map<HWND, DMARender::RenderWindow*> DMARender::hwndMap = std::map<HWND, DMARender::RenderWindow*>();
void DMARender::RenderWindow::drawOverlayHandler()
{
    static bool identifyWindows = false;
    auto pIO = ImGui::GetPlatformIO();
    static int monitor_current_idx = 0;

    ImGuiViewport* mainViewport = ImGui::GetMainViewport();
    if (!mainViewport) {
        return;
    }

    ImGui::SetNextWindowViewport(mainViewport->ID);
    ImGui::Begin("Main Menu", nullptr, ImGuiWindowFlags_None);


    static int selectedTab = 0;
    static bool followPlayerEnabled = false;
    static MapTransform mTrans = MapTransform();  // Add missing map transform for radar

    // Ensure enough space for tab bar and improve responsiveness
    ImGui::Spacing();
    ImGui::Separator();

    // ===== STATUS INFORMATION BAR =====
    ImGui::Spacing();

    // Create a horizontal status bar with system information
    ImGui::BeginGroup();
    {
        // Get current time
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        struct tm tm;
        localtime_s(&tm, &time_t);
        char timeBuffer[32];
        std::strftime(timeBuffer, sizeof(timeBuffer), "%H:%M:%S", &tm);

        // Get ImGui frame rate
        float fps = ImGui::GetIO().Framerate;

        // Status indicators with colors
        ImVec4 greenColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
        ImVec4 yellowColor = ImVec4(1.0f, 1.0f, 0.0f, 1.0f);
        ImVec4 redColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        ImVec4 cyanColor = ImVec4(0.0f, 1.0f, 1.0f, 1.0f);

        // Create horizontal layout for status items
        ImGui::TextColored(cyanColor, "Time: %s", timeBuffer);

        ImGui::SameLine();
        ImGui::Text(" | ");

        ImGui::SameLine();
        ImVec4 fpsColor = fps > 60 ? greenColor : (fps > 30 ? yellowColor : redColor);
        ImGui::TextColored(fpsColor, "FPS: %.0f", fps);

        ImGui::SameLine();
        ImGui::Text(" | ");

        ImGui::SameLine();
        // Check if we have game connection (bridge and entities)
        bool gameConnected = bridge && bridge->getOverlay();
        ImVec4 gameColor = gameConnected ? greenColor : redColor;
        ImGui::TextColored(gameColor, "Game: %s", gameConnected ? "Connected" : "Disconnected");

        ImGui::SameLine();
        ImGui::Text(" | ");

        ImGui::SameLine();
        // DMA Health - check if we have valid memory access
        bool dmaHealthy = bridge && bridge->getOverlay() && gameConnected;
        ImVec4 dmaColor = dmaHealthy ? greenColor : redColor;
        ImGui::TextColored(dmaColor, "DMA: %s", dmaHealthy ? "Healthy" : "Error");

        ImGui::SameLine();
        ImGui::Text(" | ");

        ImGui::SameLine();
        // DMA Activity indicator with detailed debug information
        static auto lastUpdateTime = std::chrono::steady_clock::now();
        static float smoothedActivity = 0.0f;
        static bool debugMode = false;

        // Get current entity processing activity
        bool espActive = false;
        bool radarActive = false;

        // Check if ESP overlay is actually enabled and visible
        if (bridge && bridge->getOverlay()) {
            espActive = true;
        }

        // Check if radar is actually being used (not just available)
        // Radar is considered active if any radar-specific features are enabled
        if (bridge && bridge->getRadar()) {
            // Check if any radar-specific windows or features are enabled
            bool radarWindowsOpen = bridge->shouldShowPlayerList() || bridge->shouldShowServerPlayerList();
            bool radarItemsEnabled = bridge->shouldShowDeadPlayers() || bridge->shouldShowVehicles() ||
                                   bridge->shouldShowGrounditems() || bridge->shouldShowBoats() ||
                                   bridge->shouldShowWeapons() || bridge->shouldShowBackpacks() ||
                                   bridge->shouldShowRare() || bridge->shouldShowFood() ||
                                   bridge->shouldShowAmmo() || bridge->shouldShowZombies() ||
                                   bridge->shouldShowAnimals();

            // Radar is active if windows are open OR items are enabled for radar display
            radarActive = radarWindowsOpen || radarItemsEnabled;
        }

        // Calculate activity level based on features in use
        auto currentTime = std::chrono::steady_clock::now();
        auto timeDelta = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - lastUpdateTime).count();

        if (timeDelta > 100) { // Update every 100ms for smoother indication
            float baseActivity = 0.0f;
            float workerLoad = 0.0f;
            float fpsBonus = 0.0f;

            // Calculate worker load based on timing profiles and active features
            if (espActive || radarActive) {
                // Get current timing profile from MultiCoreConfig
                auto profile = g_multiCoreConfig.getCurrentProfile();

                // Calculate worker intensity based on timing (lower ms = higher load)
                // Near worker: Most intensive (1-7ms) - Weight: 40%
                float nearLoad = (10.0f - profile.nearWorkerMs) / 10.0f * 40.0f;
                if (nearLoad < 0) nearLoad = 0;

                // Far worker: Medium intensive (8-20ms) - Weight: 25%
                float farLoad = (25.0f - profile.farWorkerMs) / 25.0f * 25.0f;
                if (farLoad < 0) farLoad = 0;

                // Item worker: Medium intensive (8-20ms) - Weight: 25%
                float itemLoad = (25.0f - profile.itemWorkerMs) / 25.0f * 25.0f;
                if (itemLoad < 0) itemLoad = 0;

                // Slow worker: Low intensive (400-1000ms) - Weight: 10%
                float slowLoad = (1200.0f - profile.slowWorkerMs) / 1200.0f * 10.0f;
                if (slowLoad < 0) slowLoad = 0;

                // Total worker load
                workerLoad = nearLoad + farLoad + itemLoad + slowLoad;

                // Base activity based on features
                if (!espActive && !radarActive) {
                    baseActivity = 0.0f; // Idle
                } else if (espActive && !radarActive) {
                    baseActivity = workerLoad * 0.6f; // ESP uses 60% of worker capacity
                } else if (!espActive && radarActive) {
                    baseActivity = workerLoad * 0.4f; // Radar uses 40% of worker capacity
                } else {
                    baseActivity = workerLoad; // Both use full worker capacity
                }
            }

            // FPS impact calculation (performance penalty)
            if (fps < 60) fpsBonus = 15.0f;
            else if (fps < 100) fpsBonus = 8.0f;
            else fpsBonus = 0.0f;

            float targetActivity = baseActivity + fpsBonus;

            // Smooth the activity level
            smoothedActivity = smoothedActivity * 0.8f + targetActivity * 0.2f;
            lastUpdateTime = currentTime;
        }

        // Clamp activity to 0-100%
        if (smoothedActivity > 100.0f) smoothedActivity = 100.0f;
        if (smoothedActivity < 0.0f) smoothedActivity = 0.0f;

        // Color and status based on activity level
        ImVec4 activityColor = smoothedActivity < 30.0f ? greenColor : (smoothedActivity < 60.0f ? yellowColor : redColor);
        const char* activityStatus = smoothedActivity < 20.0f ? "Idle" :
                                    (smoothedActivity < 40.0f ? "Low" :
                                    (smoothedActivity < 70.0f ? "Medium" : "High"));

        // Toggle debug mode with Ctrl+D
        if (ImGui::GetIO().KeyCtrl && ImGui::IsKeyPressed(ImGuiKey_D)) {
            debugMode = !debugMode;
        }

        if (debugMode) {
            // Show detailed debug information with worker timings
            auto profile = g_multiCoreConfig.getCurrentProfile();
            ImGui::TextColored(activityColor, "DMA: %s (%.0f%%) [E:%s R:%s F:%.0f N:%dms F:%dms I:%dms]",
                             activityStatus, smoothedActivity,
                             espActive ? "Y" : "N",
                             radarActive ? "Y" : "N",
                             fps,
                             profile.nearWorkerMs,
                             profile.farWorkerMs,
                             profile.itemWorkerMs);
        } else {
            // Normal display
            ImGui::TextColored(activityColor, "DMA Activity: %s (%.0f%%)", activityStatus, smoothedActivity);
        }

        // Add FreeType status indicator
        ImGui::SameLine();
        ImGui::Text(" | ");
        ImGui::SameLine();

#ifdef IMGUI_ENABLE_FREETYPE
        ImVec4 freetypeColor = ImVec4(0.0f, 1.0f, 0.5f, 1.0f); // Green-cyan for enabled
        ImGui::TextColored(freetypeColor, "FreeType: ✅ Active");
#else
        ImVec4 freetypeColor = ImVec4(1.0f, 0.5f, 0.0f, 1.0f); // Orange for disabled
        ImGui::TextColored(freetypeColor, "FreeType: ❌ Disabled");
#endif
    }
    ImGui::EndGroup();

    ImGui::Spacing();
    ImGui::Separator();
    ImGui::Spacing();

    // add tabs with improved responsiveness
    if (ImGui::BeginTabBar("MainMenuTabs")) {
        // ESP tab (previously first in Overlay Handler)
        if (ImGui::BeginTabItem("ESP", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 0;

            if (identifyWindows) {
                for (int i = 0; i < pIO.Monitors.size(); i++) {
                    auto pMonitor = pIO.Monitors[i];
                    ImGui::SetNextWindowPos(ImVec2(pMonitor.MainPos.x + 40, pMonitor.MainPos.y + 40));
                    ImGui::SetNextWindowSize(ImVec2(150, 250));
                    auto windowName = std::format("{}", i);
                    ImGui::Begin(windowName.c_str(), nullptr, ImGuiWindowFlags_NoDocking | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoInputs | ImGuiWindowFlags_NoTitleBar);

                    auto drawList = ImGui::GetWindowDrawList();
                    auto p = ImGui::GetCursorScreenPos();

                    ImGui::PushFont(windowIdentifyFont);
                    drawList->AddText(ImGui::GetFont(), ImGui::GetFontSize(), ImVec2(p.x, p.y), IM_COL32(255, 0, 0, 255), windowName.c_str());
                    ImGui::PopFont();
                    ImGui::End();
                }
            }

            if (overlayEnabled) {
                auto io = ImGui::GetIO();

                // Status and FPS Display
                ImGui::Text("FPS: %.1f", io.Framerate);
                ImGui::SameLine();
                if (overlayLocked) {
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "[LOCKED]");
                } else {
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "[DRAGGABLE]");
                }

                // Overlay Controls - organized in a clean row
                if (ImGui::Button("Disable Overlay")) {
                    overlayEnabled = false;
                }
                ImGui::SameLine();
                ImGui::Checkbox("Lock Position", &overlayLocked);
                ImGui::SameLine();
                if (ImGui::Button("Reset Position")) {
                    resetPosition = true;
                }

                ImGui::Separator();

                // FPS Counter Settings - organized section
                ImGui::Text("FPS Counter Settings:");

                float fpsCounterFontSize = bridge->getFpsCounterFontSize();
                float fpsCounterHorizontalPadding = bridge->getFpsCounterHorizontalPadding();
                float fpsCounterVerticalPadding = bridge->getFpsCounterVerticalPadding();

                ImGui::Text("Font Size:");
                ImGui::SameLine();
                ImGui::SetNextItemWidth(120);
                ImGui::SliderFloat("##FPSFontSize", &fpsCounterFontSize, 12.0f, 48.0f, "%.0f");
                ImGui::SameLine();
                ImGui::Text("Position:");
                ImGui::SameLine();
                ImGui::SetNextItemWidth(100);
                ImGui::SliderFloat("##FPSHorizontal", &fpsCounterHorizontalPadding, 0.0f, 1000.0f, "H:%.0f");
                ImGui::SameLine();
                ImGui::SetNextItemWidth(100);
                ImGui::SliderFloat("##FPSVertical", &fpsCounterVerticalPadding, 0.0f, 200.0f, "V:%.0f");

                bridge->setFpsCounterFontSize(fpsCounterFontSize);
                bridge->setFpsCounterHorizontalPadding(fpsCounterHorizontalPadding);
                bridge->setFpsCounterVerticalPadding(fpsCounterVerticalPadding);


                // ===== PLAYER ESP SECTION =====
                if (ImGui::CollapsingHeader("Player ESP", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();

                    bool showPlayerInfoesp = bridge->shouldShowPlayerInfoesp();
                    bool showPlayerNameFUSER = bridge->shouldShowPlayerNameFUSER();
                    bool showPlayerDistanceFUSER = bridge->shouldShowPlayerDistanceFUSER();
                    bool showPlayerHandFUSER = bridge->shouldShowPlayerHandFUSER();
                    bool showPlayerBoxFUSER = bridge->shouldShowPlayerBoxFUSER();
                    std::string followPlayerName = bridge->shouldPlayerName();
                    int playerMaxDistance = bridge->getPlayerMaxDistance();

                    ImGui::Text("Player Information Display:");
                    ImGui::InputText("Follow Player Name", &followPlayerName, sizeof(followPlayerName));
                    ImGui::Checkbox("Show Player Names", &showPlayerNameFUSER);
                    ImGui::Checkbox("Show Player Distance", &showPlayerDistanceFUSER);
                    ImGui::Checkbox("Show Player Hand Item", &showPlayerHandFUSER);
                    ImGui::Checkbox("Show Player Bounding Box", &showPlayerBoxFUSER);
                    
                    // Box Type Settings (only show if bounding box is enabled)
                    if (showPlayerBoxFUSER) {
                        ImGui::Indent();
                        
                        // Box Type Selection
                        int boundingBoxType = bridge->getBoundingBoxType();
                        const char* boxTypes[] = { "Normal Box", "Slim Box", "Corner Box", "3D Box" };
                        if (ImGui::Combo("Box Type", &boundingBoxType, boxTypes, IM_ARRAYSIZE(boxTypes))) {
                            bridge->setBoundingBoxType(boundingBoxType);
                            bridge->saveSettings();
                        }
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Normal: Standard bounding box\nSlim: Tighter fit (40% of height)\nCorner: CS2-style corner indicators only\n3D: Depth effect with back face");
                        }
                        
                        // Box Style Settings
                        ImGui::Separator();
                        ImGui::Text("Box Style Settings:");
                        
                        float boundingBoxThickness = bridge->getBoundingBoxThickness();
                        if (ImGui::SliderFloat("Box Thickness", &boundingBoxThickness, 0.5f, 3.0f, "%.1fpx")) {
                            bridge->setBoundingBoxThickness(boundingBoxThickness);
                            bridge->saveSettings();
                        }
                        
                        bool boundingBoxRounded = bridge->getBoundingBoxRounded();
                        if (ImGui::Checkbox("Rounded Corners", &boundingBoxRounded)) {
                            bridge->setBoundingBoxRounded(boundingBoxRounded);
                            bridge->saveSettings();
                        }
                        
                        if (boundingBoxRounded) {
                            float boundingBoxRounding = bridge->getBoundingBoxRounding();
                            if (ImGui::SliderFloat("Corner Radius", &boundingBoxRounding, 1.0f, 8.0f, "%.1fpx")) {
                                bridge->setBoundingBoxRounding(boundingBoxRounding);
                                bridge->saveSettings();
                            }
                        }
                        
                        bool boundingBoxAntiAliasing = bridge->getBoundingBoxAntiAliasing();
                        if (ImGui::Checkbox("Anti-Aliasing", &boundingBoxAntiAliasing)) {
                            bridge->setBoundingBoxAntiAliasing(boundingBoxAntiAliasing);
                            bridge->saveSettings();
                        }
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Smooth box edges (may impact performance)");
                        }
                        
                        ImGui::Unindent();
                    }

                    ImGui::Separator();
                    ImGui::Text("Player Distance Settings:");
                    ImGui::SliderInt("Player Max Distance", &playerMaxDistance, 1, 1500);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Maximum distance to show players");
                    }

                    bridge->setShowPlayerInfoesp(showPlayerInfoesp);
                    bridge->setShowPlayerNameFUSER(showPlayerNameFUSER);
                    bridge->setShowPlayerDistanceFUSER(showPlayerDistanceFUSER);
                    bridge->setShowPlayerHandFUSER(showPlayerHandFUSER);
                    bridge->setShowPlayerBoxFUSER(showPlayerBoxFUSER);
                    bridge->setShowPlayerName(followPlayerName);
                    bridge->setPlayerMaxDistance(playerMaxDistance);
                }

                // ===== SKELETON ESP SECTION =====
                if (ImGui::CollapsingHeader("Skeleton ESP")) {
                    ImGui::Spacing();

                    bool showPlayerSkeleton = bridge->isPlayerSkeletonEnabled();
                    bool showZombieSkeleton = bridge->isZombieSkeletonEnabled();
                    float skeletonLineThickness = bridge->getSkeletonLineThickness();
                    float playerSkeletonMaxDistance = bridge->getPlayerSkeletonMaxDistance();
                    float zombieSkeletonMaxDistance = bridge->getZombieSkeletonMaxDistance();
                    int skeletonDetailLevel = bridge->getSkeletonDetailLevel();

                    ImGui::Text("Skeleton Display Options:");
                    ImGui::Checkbox("Player Skeleton", &showPlayerSkeleton);
                    ImGui::Checkbox("Zombie Skeleton", &showZombieSkeleton);
                    
                    // Head Circle setting (DayZ mod.txt style)
                    bool headCircleEnabled = bridge->isHeadCircleEnabled();
                    ImGui::Checkbox("Head Circle (DayZ mod.txt style)", &headCircleEnabled);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Draw a circle around player/zombie heads\nSimilar to the DayZ mod.txt script approach");
                    }
                    
                    if (headCircleEnabled) {
                        ImGui::Indent();
                        float headCircleSize = bridge->getHeadCircleSize();
                        ImGui::SliderFloat("Head Circle Size", &headCircleSize, 0.05f, 2.0f, "%.2f");
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Adjust the size of head circles\n0.05 = Tiny, 0.5 = Small, 1.0 = Normal, 2.0 = Large");
                        }
                        bridge->setHeadCircleSize(headCircleSize);
                        ImGui::Unindent();
                    }
                    
                    ImGui::SliderFloat("Skeleton Line Thickness", &skeletonLineThickness, 0.1f, 3.0f, "%.1f");
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("DayZ2-style skeleton with minimal bone connections\nDefault: 0.5px for clean, thin lines");
                    }

                    // Skeleton Detail Level
                    const char* detailLevels[] = { "Minimal", "Reduced", "Medium", "Full" };
                    ImGui::Combo("Skeleton Detail Level", &skeletonDetailLevel, detailLevels, IM_ARRAYSIZE(detailLevels));
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Minimal: Basic structure only (best performance)\nReduced: Basic + some detail\nMedium: CheatsBuddy style stick figure (default)\nFull: Maximum detail with hand bones");
                    }

                    // Distance Settings
                    ImGui::Separator();
                    ImGui::Text("Distance Settings:");
                    
                    if (showPlayerSkeleton) {
                        ImGui::SliderFloat("Player Skeleton Max Distance", &playerSkeletonMaxDistance, 50.0f, 2000.0f, "%.0fm");
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to render player skeletons\nLower values improve performance");
                        }
                    }
                    
                    if (showZombieSkeleton) {
                        ImGui::SliderFloat("Zombie Skeleton Max Distance", &zombieSkeletonMaxDistance, 25.0f, 800.0f, "%.0fm");
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to render zombie skeletons\nLower values significantly improve performance");
                        }
                    }

                    // Apply settings to bridge
                    bridge->setPlayerSkeletonEnabled(showPlayerSkeleton);
                    bridge->setZombieSkeletonEnabled(showZombieSkeleton);
                    bridge->setHeadCircleEnabled(headCircleEnabled);
                    bridge->setSkeletonLineThickness(skeletonLineThickness);
                    bridge->setPlayerSkeletonMaxDistance(playerSkeletonMaxDistance);
                    bridge->setZombieSkeletonMaxDistance(zombieSkeletonMaxDistance);
                    bridge->setSkeletonDetailLevel(skeletonDetailLevel);
                }

                // ===== HEALTH BAR ESP SECTION =====
                if (ImGui::CollapsingHeader("Health Bar ESP")) {
                    ImGui::Spacing();

                    bool showPlayerHealthBar = bridge->isPlayerHealthBarEnabled();
                    bool showZombieHealthBar = bridge->isZombieHealthBarEnabled();
                float healthBarWidth = bridge->getHealthBarWidth();
                float healthBarHeight = bridge->getHealthBarHeight();
                float healthBarOffsetY = bridge->getHealthBarOffsetY();
                float healthBarMaxDistance = bridge->getHealthBarMaxDistance();
                int healthBarType = bridge->getHealthBarType();
                bool showHealthNumbers = bridge->isShowHealthNumbers();
                bool showHealthBar = bridge->isShowHealthBar();
                bool showAllHealthStats = bridge->isShowAllHealthStats();

                ImGui::Checkbox("Player Health Bar", &showPlayerHealthBar);
                ImGui::Checkbox("Zombie Health Bar", &showZombieHealthBar);

                if (showPlayerHealthBar || showZombieHealthBar) {
                    // Display Options
                    ImGui::Separator();
                    ImGui::Text("Display Options");
                    ImGui::Checkbox("Show Health Bar", &showHealthBar);
                    ImGui::SameLine();
                    ImGui::Checkbox("Show Numbers", &showHealthNumbers);
                    ImGui::Checkbox("Show All Stats (Health/Blood/Shock)", &showAllHealthStats);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Shows all health stats in a detailed format");
                    }

                    // Health Bar Type Selection (only if not showing all stats)
                    if (!showAllHealthStats) {
                        const char* healthTypes[] = { "Health (0-100)", "Blood (0-5000)", "Shock (0-100)" };
                        ImGui::Combo("Health Type", &healthBarType, healthTypes, IM_ARRAYSIZE(healthTypes));
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Note: Blood/Shock values only work on servers with UI mods.\nMost vanilla servers only show Health values.");
                        }
                    }
                }

                if (showPlayerHealthBar || showZombieHealthBar) {
                    // Health Bar Visual Settings (only if health bar is enabled)
                    if (showHealthBar) {
                        ImGui::Separator();
                        ImGui::Text("Health Bar Settings");
                        ImGui::SliderFloat("Health Bar Width", &healthBarWidth, 20.0f, 80.0f, "%.0fpx");
                        ImGui::SliderFloat("Health Bar Height", &healthBarHeight, 3.0f, 12.0f, "%.0fpx");
                    }

                    // Common Settings
                    ImGui::Separator();
                    ImGui::Text("Position & Distance");
                    ImGui::SliderFloat("Vertical Offset", &healthBarOffsetY, -30.0f, 10.0f, "%.0fpx");
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Vertical offset from entity head position (negative = above)");
                    }
                    ImGui::SliderFloat("Max Distance", &healthBarMaxDistance, 25.0f, 200.0f, "%.0fm");
                }

                    bridge->setPlayerHealthBarEnabled(showPlayerHealthBar);
                    bridge->setZombieHealthBarEnabled(showZombieHealthBar);
                    bridge->setHealthBarWidth(healthBarWidth);
                    bridge->setHealthBarHeight(healthBarHeight);
                    bridge->setHealthBarOffsetY(healthBarOffsetY);
                    bridge->setHealthBarMaxDistance(healthBarMaxDistance);
                    bridge->setHealthBarType(healthBarType);
                    bridge->setShowHealthNumbers(showHealthNumbers);
                    bridge->setShowHealthBar(showHealthBar);
                    bridge->setShowAllHealthStats(showAllHealthStats);
                }

                // ===== PERSONAL HEALTH DISPLAY SECTION =====
                if (ImGui::CollapsingHeader("Personal Health Display")) {
                    ImGui::Spacing();

                bool showPersonalHealth = bridge->isShowPersonalHealth();
                int personalHealthPosition = bridge->getPersonalHealthPosition();
                float personalHealthScale = bridge->getPersonalHealthScale();
                bool personalHealthBackground = bridge->isPersonalHealthBackground();
                bool useIndividualPositioning = bridge->isUseIndividualPositioning();
                bool showHealthLabels = bridge->isShowHealthLabels();
                bool useCustomFont = bridge->isUseCustomFont();

                ImGui::Checkbox("Show Your Health Stats", &showPersonalHealth);
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Display your own health, blood, and shock values on screen");
                }

                if (showPersonalHealth) {
                    // Display Format Options
                    ImGui::Separator();
                    ImGui::Text("Display Format");
                    ImGui::Checkbox("Show Labels (Health:, Blood:, etc.)", &showHealthLabels);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Show 'Health: 100%' vs just '100%'");
                    }
                    ImGui::Checkbox("Use Custom Font System", &useCustomFont);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Use your custom font system instead of default ImGui font");
                    }

                    // Individual Indicator Toggles
                    ImGui::Separator();
                    ImGui::Text("Individual Indicator Controls");
                    bool showHealthIndicator = bridge->isShowHealthIndicator();
                    bool showBloodIndicator = bridge->isShowBloodIndicator();
                    bool showShockIndicator = bridge->isShowShockIndicator();
                    bool showStatusIndicator = bridge->isShowStatusIndicator();

                    ImGui::Checkbox("Show Health Indicator", &showHealthIndicator);
                    ImGui::Checkbox("Show Blood Indicator", &showBloodIndicator);
                    ImGui::Checkbox("Show Shock Indicator", &showShockIndicator);
                    ImGui::Checkbox("Show Status Indicator", &showStatusIndicator);

                    bridge->setShowHealthIndicator(showHealthIndicator);
                    bridge->setShowBloodIndicator(showBloodIndicator);
                    bridge->setShowShockIndicator(showShockIndicator);
                    bridge->setShowStatusIndicator(showStatusIndicator);

                    // Positioning Mode
                    ImGui::Separator();
                    ImGui::Text("Positioning Mode");
                    ImGui::Checkbox("Individual Positioning Mode", &useIndividualPositioning);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Enable to position each health indicator separately anywhere on screen");
                    }

                    if (!useIndividualPositioning) {
                        // Original grouped positioning
                        const char* positions[] = { "Top Left", "Top Right", "Bottom Left", "Bottom Right" };
                        ImGui::Combo("Position", &personalHealthPosition, positions, IM_ARRAYSIZE(positions));
                    } else {
                        // Individual positioning controls
                        ImGui::Separator();
                        ImGui::Text("Individual Indicator Positions (0-100%% of screen)");

                        float healthX = bridge->getHealthIndicatorX();
                        float healthY = bridge->getHealthIndicatorY();
                        float bloodX = bridge->getBloodIndicatorX();
                        float bloodY = bridge->getBloodIndicatorY();
                        float shockX = bridge->getShockIndicatorX();
                        float shockY = bridge->getShockIndicatorY();
                        float statusX = bridge->getStatusIndicatorX();
                        float statusY = bridge->getStatusIndicatorY();

                        if (showHealthIndicator) {
                            ImGui::Text("Health Indicator:");
                            ImGui::SliderFloat("Health X", &healthX, 0.0f, 100.0f, "%.1f%%");
                            ImGui::SliderFloat("Health Y", &healthY, 0.0f, 100.0f, "%.1f%%");
                        }

                        if (showBloodIndicator) {
                            ImGui::Text("Blood Indicator:");
                            ImGui::SliderFloat("Blood X", &bloodX, 0.0f, 100.0f, "%.1f%%");
                            ImGui::SliderFloat("Blood Y", &bloodY, 0.0f, 100.0f, "%.1f%%");
                        }

                        if (showShockIndicator) {
                            ImGui::Text("Shock Indicator:");
                            ImGui::SliderFloat("Shock X", &shockX, 0.0f, 100.0f, "%.1f%%");
                            ImGui::SliderFloat("Shock Y", &shockY, 0.0f, 100.0f, "%.1f%%");
                        }

                        if (showStatusIndicator) {
                            ImGui::Text("Status Indicator:");
                            ImGui::SliderFloat("Status X", &statusX, 0.0f, 100.0f, "%.1f%%");
                            ImGui::SliderFloat("Status Y", &statusY, 0.0f, 100.0f, "%.1f%%");
                        }

                        bridge->setHealthIndicatorX(healthX);
                        bridge->setHealthIndicatorY(healthY);
                        bridge->setBloodIndicatorX(bloodX);
                        bridge->setBloodIndicatorY(bloodY);
                        bridge->setShockIndicatorX(shockX);
                        bridge->setShockIndicatorY(shockY);
                        bridge->setStatusIndicatorX(statusX);
                        bridge->setStatusIndicatorY(statusY);
                    }

                    ImGui::SliderFloat("Text Scale", &personalHealthScale, 0.5f, 2.0f, "%.1fx");
                    ImGui::Checkbox("Show Background", &personalHealthBackground);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Show semi-transparent background behind health text");
                    }
                }

                    bridge->setShowPersonalHealth(showPersonalHealth);
                    bridge->setPersonalHealthPosition(personalHealthPosition);
                    bridge->setPersonalHealthScale(personalHealthScale);
                    bridge->setPersonalHealthBackground(personalHealthBackground);
                    bridge->setUseIndividualPositioning(useIndividualPositioning);
                    bridge->setShowHealthLabels(showHealthLabels);
                    bridge->setUseCustomFont(useCustomFont);
                }

                // ===== ENTITY ESP SECTION =====
                if (ImGui::CollapsingHeader("Entity ESP", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();

                    bool showZombiesFUSER = bridge->shouldShowZombiesFUSER();
                    bool showZombieBoxFUSER = bridge->shouldShowZombieBoxFUSER();
                    bool showZombieNameFUSER = bridge->shouldShowZombieNameFUSER();
                    bool showZombieDistanceFUSER = bridge->shouldShowZombieDistanceFUSER();
                    bool showAnimalsFUSER = bridge->shouldShowAnimalsFUSER();
                    bool showAnimalBoxFUSER = bridge->shouldShowAnimalBoxFUSER();
                    bool showAnimalNameFUSER = bridge->shouldShowAnimalNameFUSER();
                    bool showDeadAnimalsFUSER = bridge->shouldShowDeadAnimalsFUSER();
                    bool showDeadPlayersFUSER = bridge->shouldShowDeadPlayersFUSER();
                    bool showVehiclesFUSER = bridge->shouldShowVehiclesFUSER();
                    bool showBoatsFUSER = bridge->shouldShowBoatsFUSER();
                    bool showDebugFUSER = bridge->shouldShowDebugFUSER();
                    int ZombieDistanceFUSER = bridge->shouldZombieDistanceFUSER();
                    int LootDebugDistance = bridge->shouldLootDebugDistance();

                    // Entity distance settings
                    int animalMaxDistance = bridge->getAnimalMaxDistance();
                    int vehicleMaxDistance = bridge->getVehicleMaxDistance();
                    int boatMaxDistance = bridge->getBoatMaxDistance();
                    int deadPlayerMaxDistance = bridge->getDeadPlayerMaxDistance();
                    int deadAnimalMaxDistance = bridge->getDeadAnimalMaxDistance();
                    int playerMaxDistance = bridge->getPlayerMaxDistance();

                    ImGui::Text("Living Entities:");
                    ImGui::Checkbox("Show Zombies", &showZombiesFUSER);
                    if (showZombiesFUSER) {
                        ImGui::Indent();
                        ImGui::Checkbox("Show Zombie Bounding Boxes", &showZombieBoxFUSER);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Toggle bounding boxes around zombies");
                        }
                        ImGui::Checkbox("Show Zombie Names", &showZombieNameFUSER);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Display zombie type names");
                        }
                        ImGui::Checkbox("Show Zombie Distance", &showZombieDistanceFUSER);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Display distance to zombies");
                        }
                        ImGui::SliderInt("Zombie Max Distance", &ZombieDistanceFUSER, 1, 300);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to show zombies");
                        }
                        ImGui::Unindent();
                    }

                    ImGui::Checkbox("Show Live Animals", &showAnimalsFUSER);
                    if (showAnimalsFUSER) {
                        ImGui::Indent();
                        ImGui::Checkbox("Show Animal Bounding Boxes", &showAnimalBoxFUSER);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Toggle bounding boxes around animals");
                        }
                        ImGui::Checkbox("Show Animal Names", &showAnimalNameFUSER);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Display animal type names");
                        }
                        ImGui::SliderInt("Animal Max Distance", &animalMaxDistance, 1, 1000);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to show live animals");
                        }
                        ImGui::Unindent();
                    }

                    ImGui::Separator();
                    ImGui::Text("Dead Entities:");
                    ImGui::Checkbox("Show Dead Players", &showDeadPlayersFUSER);
                    if (showDeadPlayersFUSER) {
                        ImGui::Indent();
                        ImGui::SliderInt("Dead Player Max Distance", &deadPlayerMaxDistance, 1, 500);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to show dead players");
                        }
                        ImGui::Unindent();
                    }

                    ImGui::Checkbox("Show Dead Animals", &showDeadAnimalsFUSER);
                    if (showDeadAnimalsFUSER) {
                        ImGui::Indent();
                        ImGui::SliderInt("Dead Animal Max Distance", &deadAnimalMaxDistance, 1, 500);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to show dead animals");
                        }
                        ImGui::Unindent();
                    }

                    ImGui::Separator();
                    ImGui::Text("Vehicles:");
                    ImGui::Checkbox("Show Cars", &showVehiclesFUSER);
                    if (showVehiclesFUSER) {
                        ImGui::Indent();
                        ImGui::SliderInt("Vehicle Max Distance", &vehicleMaxDistance, 1, 2000);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to show vehicles/cars");
                        }
                        ImGui::Unindent();
                    }

                    ImGui::Checkbox("Show Boats", &showBoatsFUSER);
                    if (showBoatsFUSER) {
                        ImGui::Indent();
                        ImGui::SliderInt("Boat Max Distance", &boatMaxDistance, 1, 2000);
                        if (ImGui::IsItemHovered()) {
                            ImGui::SetTooltip("Maximum distance to show boats");
                        }
                        ImGui::Unindent();
                    }

                    ImGui::Separator();
                    ImGui::Text("Debug Options:");
                    ImGui::Checkbox("Show Debug Info", &showDebugFUSER);
                    if (showDebugFUSER) {
                        ImGui::SliderInt("Debug Distance", &LootDebugDistance, 1, 300);
                    }

                    bridge->setShowZombiesFUSER(showZombiesFUSER);
                    bridge->setShowZombieBoxFUSER(showZombieBoxFUSER);
                    bridge->setShowZombieNameFUSER(showZombieNameFUSER);
                    bridge->setShowZombieDistanceFUSER(showZombieDistanceFUSER);
                    bridge->setShowAnimalsFUSER(showAnimalsFUSER);
                    bridge->setShowAnimalBoxFUSER(showAnimalBoxFUSER);
                    bridge->setShowAnimalNameFUSER(showAnimalNameFUSER);
                    bridge->setShowDeadAnimalsFUSER(showDeadAnimalsFUSER);
                    bridge->setShowDeadPlayersFUSER(showDeadPlayersFUSER);
                    bridge->setShowVehiclesFUSER(showVehiclesFUSER);
                    bridge->setShowBoatsFUSER(showBoatsFUSER);
                    bridge->setShowDebugFUSER(showDebugFUSER);
                    bridge->setZombieDistance(ZombieDistanceFUSER);
                    bridge->setLootDebugDistance(LootDebugDistance);

                    // Set entity distance settings
                    bridge->setAnimalMaxDistance(animalMaxDistance);
                    bridge->setVehicleMaxDistance(vehicleMaxDistance);
                    bridge->setBoatMaxDistance(boatMaxDistance);
                    bridge->setDeadPlayerMaxDistance(deadPlayerMaxDistance);
                    bridge->setDeadAnimalMaxDistance(deadAnimalMaxDistance);
                    bridge->setPlayerMaxDistance(playerMaxDistance);
                }

                // ===== ITEM ESP SECTION =====
                if (ImGui::CollapsingHeader("Item ESP Controls")) {
                    ImGui::Spacing();

                    ImGui::Text("Item ESP Toggle Keybind:");
                    ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Supports: Keys, Mouse buttons, Scroll wheel");
                static int selectedKey = bridge->getItemESPToggleKey();
                static bool isCapturingKey = false;
                static std::string keyDisplayName = getKeyName(selectedKey);

                if (ImGui::Button(isCapturingKey ? "Press any key..." : keyDisplayName.c_str(), ImVec2(120, 0))) {
                    isCapturingKey = true;
                }

                if (isCapturingKey) {
                    // Check mouse buttons first
                    int mouseButtons[] = {0x01, 0x02, 0x04, 0x05, 0x06}; // Left, Right, Middle, X1, X2
                    for (int mouseBtn : mouseButtons) {
                        if (GetAsyncKeyState(mouseBtn) & 0x8000) {
                            selectedKey = mouseBtn;
                            keyDisplayName = getKeyName(mouseBtn);
                            bridge->setItemESPToggleKey(mouseBtn);
                            bridge->saveSettings();
                            isCapturingKey = false;
                            break;
                        }
                    }

                    // Check keyboard keys if no mouse button pressed
                    if (isCapturingKey) {
                        for (int key = 0x08; key <= 0x87; key++) { // Check common keys
                            // Skip mouse button codes to avoid conflicts
                            if (key == 0x01 || key == 0x02 || key == 0x04 || key == 0x05 || key == 0x06) {
                                continue;
                            }
                            if (GetAsyncKeyState(key) & 0x8000) {
                                selectedKey = key;
                                keyDisplayName = getKeyName(key);
                                bridge->setItemESPToggleKey(key);
                                bridge->saveSettings();
                                isCapturingKey = false;
                                break;
                            }
                        }
                    }
                }

                    ImGui::SameLine();
                    bool itemESPState = bridge->isItemESPEnabled();
                    ImGui::Text("Status: %s", itemESPState ? "ON" : "OFF");
                }

            }
            else {
                ImGui::InputInt("Overlay Width", &overlayWidth);
                ImGui::InputInt("Overlay Height", &overlayHeight);

                if (ImGui::Button("Begin Overlay")) {
                    overlayEnabled = true;
                }
            }
            //ImGui::End();

            ImGui::EndTabItem();
        }

        // Radar tab (moved from Radar Settings window)
        if (ImGui::BeginTabItem("Radar", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 1;

            // Map Selection and Radar Setup
            auto maps = bridge->getMapManager()->getMaps();
            if (maps.size() > 0) {
                static int map_current_index = 0;

                if (bridge->getMapManager()->isMapSelected()) {
                    if (ImGui::Button("Stop Radar"))
                        bridge->getMapManager()->clearMap();
                }
                else {
                    auto previewString = maps[map_current_index]->getName();
                    if (ImGui::BeginCombo("Map", previewString.c_str())) {
                        for (int i = 0; i < maps.size(); i++) {
                            bool isSelected = i == map_current_index;
                            if (ImGui::Selectable(maps[i]->getName().c_str(), isSelected))
                                map_current_index = i;
                        }
                        ImGui::EndCombo();
                    }
                    if (ImGui::Button("Start Radar")) {
                        // Initialize map transform when starting radar
                        mTrans.mapZoom = 0.3;
                        mTrans.dragOffsetX = 0;
                        mTrans.dragOffsetY = 0;
                        bridge->getMapManager()->selectMap(map_current_index);
                    }
                }

                // filter settings and checkbox
                if (bridge->getMapManager()->isMapSelected()) {
                    bool showVehicles = bridge->shouldShowVehicles();
                    bool showBoats = bridge->shouldShowBoats();
                    bool showZombies = bridge->shouldShowZombies();
                    bool showAnimals = bridge->shouldShowAnimals();
                    int ZombiesBlipSize = bridge->shouldZombiesBlipSize();
                    int AnimalsBlipSize = bridge->shouldAnimalsBlipSize();

                    std::string followPlayerName = bridge->shouldPlayerName();
                    int BlipSize = bridge->shouldBlipSize();
                    int Aimlinelength = bridge->shouldAimlinelength();

                    // fonts
                    int RadarFont = bridge->shouldRadarFont();

                    ImGui::Checkbox("Follow Player", &followPlayerEnabled);
                    ImGui::InputText("Name", &followPlayerName, sizeof(followPlayerName));
                    ImGui::SliderInt("P Blip", &BlipSize, 2, 15);
                    ImGui::SliderInt("Aimline", &Aimlinelength, 1, 100);
                    ImGui::SliderInt("P Font", &RadarFont, 10, 32);
                    ImGui::Checkbox("Zombies", &showZombies);
                    if (showZombies) {
                        ImGui::SliderInt("Z Blip", &ZombiesBlipSize, 2, 15);
                    }
                    ImGui::Checkbox("Animals", &showAnimals);
                    if (showAnimals) {
                        ImGui::SliderInt("A Blip", &AnimalsBlipSize, 2, 15);
                    }
                    ImGui::Checkbox("Cars", &showVehicles);
                    ImGui::Checkbox("Boats", &showBoats);

                    ImGui::Separator();
                    ImGui::Text("Dead Entities:");

                    // Get dead entities settings from bridge
                    bool showDeadPlayers = bridge->shouldShowDeadPlayers();
                    bool showDeadAnimals = bridge->shouldShowDeadAnimals();

                    ImGui::Checkbox("Dead Players", &showDeadPlayers);
                    ImGui::Checkbox("Dead Animals", &showDeadAnimals);

                    ImGui::Separator();
                    ImGui::Text("Player Information:");

                    // Get player info settings from bridge
                    bool showPlayerNameRadar = bridge->shouldShowPlayerNameRadar();
                    bool showPlayerDistanceRadar = bridge->shouldShowPlayerDistanceRadar();
                    bool showPlayerHandRadar = bridge->shouldShowPlayerHandRadar();

                    ImGui::Checkbox("Player Name", &showPlayerNameRadar);
                    ImGui::Checkbox("Player Distance", &showPlayerDistanceRadar);
                    ImGui::Checkbox("Item in Hand", &showPlayerHandRadar);

                    // Set all values back to bridge
                    bridge->setShowVehicles(showVehicles);
                    bridge->setShowBoats(showBoats);
                    bridge->setShowZombies(showZombies);
                    bridge->setShowAnimals(showAnimals);
                    bridge->setShowPlayerName(followPlayerName);
                    bridge->setShowBlipSize(BlipSize);
                    bridge->setAimlinelength(Aimlinelength);
                    bridge->setZombiesBlipSize(ZombiesBlipSize);
                    bridge->setAnimalsBlipSize(AnimalsBlipSize);
                    bridge->setShowDeadPlayers(showDeadPlayers);
                    bridge->setShowDeadAnimals(showDeadAnimals);
                    bridge->setShowPlayerNameRadar(showPlayerNameRadar);
                    bridge->setShowPlayerDistanceRadar(showPlayerDistanceRadar);
                    bridge->setShowPlayerHandRadar(showPlayerHandRadar);

                    // font
                    bridge->setRadarFont(RadarFont);
                }
            } else {
                ImGui::Text("No maps available!");
            }
            ImGui::EndTabItem();
        }

        // Loot Tab (now combining both loot tabs)
        if (ImGui::BeginTabItem("Loot", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 2;

            // Get all ESP settings
            bool showBackpacksFUSER = bridge->shouldShowBackpacksFUSER();
            bool showClothingFUSER = bridge->shouldShowClothingFUSER();
            bool showOpticsFUSER = bridge->shouldShowOpticsFUSER();
            bool showExplosivesFUSER = bridge->shouldShowExplosivesFUSER();
            bool showWeaponsFUSER = bridge->shouldShowWeaponsFUSER();
            bool showMeleeFUSER = bridge->shouldShowMeleeFUSER();
            bool showProxyMagazinesFUSER = bridge->shouldShowProxyMagazinesFUSER();
            bool showAmmoFUSER = bridge->shouldShowAmmoFUSER();
            bool showFoodFUSER = bridge->shouldShowFoodFUSER();
            bool showGrounditemsFUSER = bridge->shouldShowGrounditemsFUSER();
            bool showRareFUSER = bridge->shouldShowRareFUSER();
            bool showContainerFUSER = bridge->shouldShowContainerFUSER();
            bool showCookingFUSER = bridge->shouldShowCookingFUSER();
            bool showCampingFUSER = bridge->shouldShowCampingFUSER();
            bool showStashFUSER = bridge->shouldShowStashFUSER();
            int showItemDistance = bridge->shouldlootDistanceFUSER();
            bool showBaseFUSER = bridge->shouldShowBaseFUSER();

            // Get all Radar settings
            bool showGrounditems = bridge->shouldShowGrounditems();
            bool showClothing = bridge->shouldShowClothing();
            bool showExplosives = bridge->shouldShowExplosives();
            bool showWeapons = bridge->shouldShowWeapons();
            bool showMelee = bridge->shouldShowMelee();
            bool showProxyMagazines = bridge->shouldShowProxyMagazines();
            bool showBackpacks = bridge->shouldShowBackpacks();
            bool showFood = bridge->shouldShowFood();
            bool showAmmo = bridge->shouldShowAmmo();
            bool showRare = bridge->shouldShowRare();
            bool showBase = bridge->shouldShowBase();
            bool showOptics = bridge->shouldShowOptics();
            bool showContainer = bridge->shouldShowContainer();
            bool showCooking = bridge->shouldShowCooking();
            bool showCamping = bridge->shouldShowCamping();
            bool showStash = bridge->shouldShowStash();
            int LootDistanceDeadzone = bridge->shouldLootDistanceDeadzone();
            int BlipSize2 = bridge->shouldBlipSize2();
            int RadarFont2 = bridge->shouldRadarFont2();

            // Create two-column layout for better organization
            if (ImGui::BeginTable("LootSettings", 2, ImGuiTableFlags_BordersInnerV | ImGuiTableFlags_Resizable)) {
                
                // Left column - ESP Settings
                ImGui::TableNextColumn();
                if (ImGui::CollapsingHeader("ESP Overlay Settings", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();
                    
                                         ImGui::Spacing();
                     
                     // Equipment section
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Equipment:");
            ImGui::Checkbox("Backpacks", &showBackpacksFUSER);
            ImGui::Checkbox("Clothing", &showClothingFUSER);
            ImGui::Checkbox("Optics", &showOpticsFUSER);

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            // Container Contents section
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Container Contents:");
            bool showContainerContents = bridge->shouldShowContainerContents();
            if (ImGui::Checkbox("Show Container Contents", &showContainerContents)) {
                bridge->setShowContainerContents(showContainerContents);
                bridge->saveSettings();
            }

            if (showContainerContents) {
                int maxItems = bridge->getContainerContentsMaxItems();
                if (ImGui::SliderInt("Max Items per Container", &maxItems, 1, 20)) {
                    bridge->setContainerContentsMaxItems(maxItems);
                    bridge->saveSettings();
                }

                float maxDistance = bridge->getContainerContentsMaxDistance();
                if (ImGui::SliderFloat("Max Distance", &maxDistance, 10.0f, 200.0f, "%.0fm")) {
                    bridge->setContainerContentsMaxDistance(maxDistance);
                    bridge->saveSettings();
                }
            }
                    
                    ImGui::Spacing();
                    
                    // Weapons section
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Weapons & Ammo:");
                    ImGui::Checkbox("Weapons", &showWeaponsFUSER);
                    ImGui::Checkbox("Melee Weapons", &showMeleeFUSER);
                    ImGui::Checkbox("Magazines", &showProxyMagazinesFUSER);
                    ImGui::Checkbox("Ammo", &showAmmoFUSER);
                    ImGui::Checkbox("Explosives", &showExplosivesFUSER);
                    
                    ImGui::Spacing();
                    
                    // Other items section
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Items:");
                    ImGui::Checkbox("Food & Drinks", &showFoodFUSER);
                    ImGui::Checkbox("Ground Items", &showGrounditemsFUSER);
                    ImGui::Checkbox("Base Building", &showBaseFUSER);
                    ImGui::Checkbox("Rare Items", &showRareFUSER);
                    
                    ImGui::Spacing();
                    
                    // Container categories section
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Containers:");
                    ImGui::Checkbox("Storage Containers", &showContainerFUSER);
                    ImGui::Checkbox("Cooking Items", &showCookingFUSER);
                    ImGui::Checkbox("Camping Items", &showCampingFUSER);
                    ImGui::Checkbox("Stash Containers", &showStashFUSER);
                    
                    ImGui::Spacing();
                    
                    // Distance settings
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Settings:");
                    ImGui::SliderInt("ESP Distance", &showItemDistance, 1, 1000);
                    
                    ImGui::Spacing();
                    
                    // Debug settings
                    ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Debug:");
            bool enableESPDebugLogging = bridge->getEnableESPDebugLogging();
                    if (ImGui::Checkbox("Debug Logging", &enableESPDebugLogging)) {
                bridge->setEnableESPDebugLogging(enableESPDebugLogging);
                bridge->saveSettings();
            }
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Creates esp_debug.log and all_entities.log");
                    }
                }
                
                // Right column - Radar Settings
                ImGui::TableNextColumn();
                if (ImGui::CollapsingHeader("Radar Settings", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();
                    
                    // Entities section
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 1.0f, 1.0f), "Equipment:");
                    ImGui::Checkbox("Backpacks##radar", &showBackpacks);
                    ImGui::Checkbox("Clothing##radar", &showClothing);
                    ImGui::Checkbox("Optics##radar", &showOptics);
                    
                    ImGui::Spacing();
                    
                    // Weapons section
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 1.0f, 1.0f), "Weapons & Ammo:");
                    ImGui::Checkbox("Weapons##radar", &showWeapons);
                    ImGui::Checkbox("Melee Weapons##radar", &showMelee);
                    ImGui::Checkbox("Magazines##radar", &showProxyMagazines);
                    ImGui::Checkbox("Ammo##radar", &showAmmo);
                    ImGui::Checkbox("Explosives##radar", &showExplosives);
                    
                    ImGui::Spacing();
                    
                    // Other items section
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 1.0f, 1.0f), "Items:");
                    ImGui::Checkbox("Food & Drinks##radar", &showFood);
                    ImGui::Checkbox("Ground Items##radar", &showGrounditems);
                    ImGui::Checkbox("Base Building##radar", &showBase);
                    ImGui::Checkbox("Rare Items##radar", &showRare);
                    
                    ImGui::Spacing();
                    
                    // Container categories section
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 1.0f, 1.0f), "Containers:");
                    ImGui::Checkbox("Storage Containers##radar", &showContainer);
                    ImGui::Checkbox("Cooking Items##radar", &showCooking);
                    ImGui::Checkbox("Camping Items##radar", &showCamping);
                    ImGui::Checkbox("Stash Containers##radar", &showStash);
                    
                    ImGui::Spacing();
                    
                    // Radar-specific settings
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 1.0f, 1.0f), "Radar Settings:");
                    ImGui::SliderInt("Deadzone Distance", &LootDistanceDeadzone, 0, 300);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Minimum distance before items appear on radar");
                    }
                    
                    ImGui::SliderInt("Blip Size", &BlipSize2, 2, 15);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Size of loot icons on radar");
                    }
                    
                    ImGui::SliderInt("Font Size", &RadarFont2, 10, 32);
                    if (ImGui::IsItemHovered()) {
                        ImGui::SetTooltip("Text size for loot labels on radar");
                    }
                }
                
                ImGui::EndTable();
            }

            // Apply ESP settings
            bridge->setShowBackpacksFUSER(showBackpacksFUSER);
            bridge->setShowClothingFUSER(showClothingFUSER);
            bridge->setShowOpticsFUSER(showOpticsFUSER);
            bridge->setShowExplosivesFUSER(showExplosivesFUSER);
            bridge->setShowWeaponsFUSER(showWeaponsFUSER);
            bridge->setShowMeleeFUSER(showMeleeFUSER);
            bridge->setShowProxyMagazinesFUSER(showProxyMagazinesFUSER);
            bridge->setShowAmmoFUSER(showAmmoFUSER);
            bridge->setShowFoodFUSER(showFoodFUSER);
            bridge->setShowGrounditemsFUSER(showGrounditemsFUSER);
            bridge->setShowBaseFUSER(showBaseFUSER);
            bridge->setShowRareFUSER(showRareFUSER);
            bridge->setShowContainerFUSER(showContainerFUSER);
            bridge->setShowCookingFUSER(showCookingFUSER);
            bridge->setShowCampingFUSER(showCampingFUSER);
            bridge->setShowStashFUSER(showStashFUSER);
            bridge->setlootDistance(showItemDistance);

            // Apply Radar settings
            bridge->setShowGrounditems(showGrounditems);
            bridge->setShowClothing(showClothing);
            bridge->setShowExplosives(showExplosives);
            bridge->setShowWeapons(showWeapons);
            bridge->setShowMelee(showMelee);
            bridge->setShowProxyMagazines(showProxyMagazines);
            bridge->setShowBackpacks(showBackpacks);
            bridge->setShowFood(showFood);
            bridge->setShowAmmo(showAmmo);
            bridge->setShowRare(showRare);
            bridge->setShowBase(showBase);
            bridge->setShowOptics(showOptics);
            bridge->setShowContainer(showContainer);
            bridge->setShowCooking(showCooking);
            bridge->setShowCamping(showCamping);
            bridge->setShowStash(showStash);
            bridge->setLootDistanceDeadzone(LootDistanceDeadzone);
            bridge->setShowBlipSize2(BlipSize2);
            bridge->setRadarFont2(RadarFont2);

            ImGui::EndTabItem();
        }

        // Loot Filter Tab (moved from Radar Settings)
        if (ImGui::BeginTabItem("Loot Filter", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 3;
            drawLootListTab();
            ImGui::EndTabItem();
        }

        // Extra Tab (moved here for correct order)
        if (ImGui::BeginTabItem("Extra", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 4;

            bool showPlayerList = bridge->shouldShowPlayerList();
            bool showServerPlayerList = bridge->shouldShowServerPlayerList();

            // Player List Controls Section
            ImGui::Text("Player List Controls");
            ImGui::Separator();
            ImGui::Spacing();

            // Create organized layout for player list controls
            ImGui::BeginGroup();
            {
                ImGui::Text("Player List Features:");
                ImGui::Spacing();

                // Local Player List
                ImGui::Checkbox("Enable Local Player List Window", &showPlayerList);
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Shows a separate window with players currently in your area\n"
                                    "Displays player names and distances\n"
                                    "Updates in real-time as players move");
                }

                ImGui::Spacing();

                // Server Player List
                ImGui::Checkbox("Enable Server Player List Window", &showServerPlayerList);
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Shows a separate window with all players on the server\n"
                                    "Displays player names and Steam IDs\n"
                                    "Click Steam IDs to copy profile links");
                }

                ImGui::Spacing();
                ImGui::Separator();
                ImGui::Spacing();

                // Information section
                ImGui::TextColored(ImVec4(0.7f, 0.9f, 1.0f, 1.0f), "Player List Information:");
                ImGui::Spacing();

                ImGui::BulletText("Player lists appear as separate windows when enabled");
                ImGui::BulletText("Local list shows nearby players with real-time distances");
                ImGui::BulletText("Server list shows all connected players with Steam info");
                ImGui::BulletText("Both lists update automatically in the background");
                ImGui::BulletText("Windows can be moved, resized, and positioned independently");

                ImGui::Spacing();
                ImGui::Separator();
                ImGui::Spacing();

                // Status section
                ImGui::TextColored(ImVec4(0.9f, 0.9f, 0.7f, 1.0f), "Current Status:");
                ImGui::Spacing();

                ImGui::Text("Local Player List: %s", showPlayerList ? "Enabled" : "Disabled");
                ImGui::Text("Server Player List: %s", showServerPlayerList ? "Enabled" : "Disabled");

                if (showPlayerList || showServerPlayerList) {
                    ImGui::Spacing();
                    ImGui::TextColored(ImVec4(0.7f, 1.0f, 0.7f, 1.0f), "Tip: Look for the player list windows on your screen!");
                }
            }
            ImGui::EndGroup();

            bridge->setShowPlayerList(showPlayerList);
            bridge->setShowServerPlayerList(showServerPlayerList);

            ImGui::EndTabItem();
        }

        // Fonts Tab (from original Overlay Handler)
        if (ImGui::BeginTabItem("Fonts", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 5;

        // FreeType Status and Comparison Section
        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.5f, 1.0f), "🔧 FreeType Font Rendering Status");
        ImGui::Spacing();

#ifdef IMGUI_ENABLE_FREETYPE
        ImGui::BulletText("✅ FreeType is ENABLED - Enhanced font quality active");
        ImGui::BulletText("✅ 4x4 oversampling for crystal clear text");
        ImGui::BulletText("✅ Advanced hinting for better readability");
        ImGui::BulletText("✅ Improved anti-aliasing and edge smoothing");

        // Visual comparison text samples
        ImGui::Spacing();
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "Font Quality Sample:");
        ImGui::Text("Enhanced Text: The quick brown fox jumps over the lazy dog 123456789");
        ImGui::Text("Small Text: abcdefghijklmnopqrstuvwxyz ABCDEFGHIJKLMNOPQRSTUVWXYZ");
        ImGui::Text("Numbers: 0123456789 !@#$%^&*()_+-=[]{}|;':\",./<>?");
#else
        ImGui::BulletText("❌ FreeType is DISABLED - Using standard STB rendering");
        ImGui::BulletText("❌ Limited font quality and anti-aliasing");
        ImGui::BulletText("❌ No advanced hinting or oversampling");
#endif

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        ImGui::Text("ESP Font Settings");
        ImGui::Separator();

        // Get current font settings from bridge
            static int selectedFontIndex = 1; // Default to Calibri
        if (bridge) {
            try {
                selectedFontIndex = bridge->getSelectedFontIndex();
            }
            catch (...) {
                // Keep current value if bridge method fails
            }
        }
        
        // Get dynamic font list from bridge instead of hardcoded list
        std::vector<std::string> windowsFonts;
        if (bridge) {
            try {
                const auto& availableFonts = bridge->getAvailableFontNames();
                windowsFonts = availableFonts;
                
                // Ensure selectedFontIndex is valid for current font list
                if (selectedFontIndex >= (int)windowsFonts.size()) {
                    selectedFontIndex = 0; // Reset to first available font
                    bridge->setSelectedFontIndex(selectedFontIndex);
                }
            }
            catch (...) {
                // Fallback to basic fonts if dynamic detection fails
                windowsFonts = {
            "Arial",
            "Calibri",
            "Tahoma",
            "Times New Roman",
            "Verdana"
        };
            }
        } else {
            // Fallback fonts when no bridge available
            windowsFonts = {
                "Arial",
                "Calibri",
                "Tahoma", 
                "Times New Roman",
                "Verdana"
            };
        }

        // Font selection dropdown
        ImGui::Text("Select ESP Font:");
        
        // Add refresh button next to font selector
        ImGui::SameLine();
        if (ImGui::Button("Refresh Fonts")) {
            if (bridge) {
                bridge->clearFontCache();
                std::cout << "[FONT UI] Font cache cleared, will rescan on next access" << std::endl;
            }
        }
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Rescan system for newly installed fonts\nDetects fonts from:\n• C:\\Windows\\Fonts\\\n• User font directory");
        }
        
        const char* currentFontName = (selectedFontIndex >= 0 && selectedFontIndex < (int)windowsFonts.size()) 
            ? windowsFonts[selectedFontIndex].c_str() 
            : "Unknown Font";
            
        if (ImGui::BeginCombo("##FontSelector", currentFontName)) {
            for (int i = 0; i < windowsFonts.size(); i++) {
                bool isSelected = (selectedFontIndex == i);
                if (ImGui::Selectable(windowsFonts[i].c_str(), isSelected)) {
                        selectedFontIndex = i;
                    if (bridge) {
                        bridge->setSelectedFontIndex(i);
                        bridge->saveSettings();
                        std::cout << "[FONT UI] Selected font: " << windowsFonts[i] << " (index " << i << ")" << std::endl;
                    }
                }
                if (isSelected) {
                    ImGui::SetItemDefaultFocus();
                }
            }
            ImGui::EndCombo();
        }

        // Show font info
        ImGui::Separator();
        ImGui::Text("Font Information:");
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Total fonts available: %d", (int)windowsFonts.size());
        if (bridge && selectedFontIndex >= 0 && selectedFontIndex < (int)windowsFonts.size()) {
            try {
                std::string selectedFile = bridge->getSelectedFontFile();
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Selected: %s (%s)", 
                    windowsFonts[selectedFontIndex].c_str(), selectedFile.c_str());
            }
            catch (...) {
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Selected: %s", windowsFonts[selectedFontIndex].c_str());
            }
        }

        // Base Font Size (pixel size when loading fonts) - declare early for restart check
        static float baseFontSize = 28.0f;
        if (bridge) {
            try {
                baseFontSize = bridge->getBaseFontSize();
            }
            catch (...) {
                // Use default value if bridge fails
            }
        }

        // Show restart reminder if font was changed
            static int lastAppliedFontIndex = 1;
            static float lastAppliedBaseFontSize = 28.0f;
        if (selectedFontIndex != lastAppliedFontIndex || baseFontSize != lastAppliedBaseFontSize) {
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "[Restart Required]");
        }

        ImGui::Separator();

            // ESP Text Size
        static float espTextSize = 1.0f;
        if (bridge) {
            try {
                espTextSize = bridge->getESPTextSize();
            }
            catch (...) {
                // Use default value if bridge fails
            }
        }

            if (ImGui::SliderFloat("ESP Text Size", &espTextSize, 0.1f, 3.0f, "%.1f")) {
            if (bridge) {
                bridge->setESPTextSize(espTextSize);
                bridge->saveSettings();
            }
        }
        
        ImGui::Text("Base Font Size (Pixels):");
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Base pixel size when loading fonts (requires restart)\nRange: 8-72px, Default: 28px\nHigher values = sharper text but more memory usage");
        }
        
        if (ImGui::SliderFloat("##BaseFontSize", &baseFontSize, 8.0f, 72.0f, "%.0fpx")) {
            if (bridge) {
                bridge->setBaseFontSize(baseFontSize);
                bridge->saveSettings();
            }
        }

        ImGui::Separator();

            // Font Style Settings
            ImGui::Text("Font Style Settings:");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Advanced font rendering options");

        static bool fontBold = false;
        static bool fontOutline = true;
        static bool fontShadow = false;

        if (bridge) {
            try {
                fontBold = bridge->getFontBold();
                fontOutline = bridge->getFontOutline();
                fontShadow = bridge->getFontShadow();
            }
            catch (...) {
                    // Keep current values if bridge access fails
            }
        }

            if (ImGui::Checkbox("Bold Font", &fontBold)) {
            if (bridge) {
                bridge->setFontBold(fontBold);
                bridge->saveSettings();
            }
        }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Enable bold text rendering (requires restart)");
            }

            if (ImGui::Checkbox("Font Outline", &fontOutline)) {
            if (bridge) {
                bridge->setFontOutline(fontOutline);
                bridge->saveSettings();
            }
        }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Add outline to text for better visibility");
            }

            if (ImGui::Checkbox("Font Shadow", &fontShadow)) {
            if (bridge) {
                bridge->setFontShadow(fontShadow);
                bridge->saveSettings();
            }
        }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Add shadow to text for enhanced readability");
        }

        // Font Alpha (Transparency) Setting
        static float fontAlpha = 1.0f;
            if (bridge) {
            try {
                fontAlpha = bridge->getFontAlpha();
            }
            catch (...) {
                // Keep current value if bridge access fails
        }
        }

        ImGui::Text("Font Transparency:");
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Adjust font transparency\n1.0 = fully opaque, 0.0 = fully transparent\nRange: 0.0-1.0, Default: 1.0");
        }
        if (ImGui::SliderFloat("##FontAlpha", &fontAlpha, 0.0f, 1.0f, "%.2f")) {
            if (bridge) {
                bridge->setFontAlpha(fontAlpha);
                bridge->saveSettings();
            }
        }

        // Font Shadow Offset Setting
        static float fontShadowOffset = 1.0f;
        if (bridge) {
            try {
                fontShadowOffset = bridge->getFontShadowOffset();
            }
            catch (...) {
                // Keep current value if bridge access fails
            }
        }

        ImGui::Text("Font Shadow Distance:");
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Adjust shadow offset distance\n0.0 = no shadow offset, 5.0 = large shadow\nRange: 0.0-5.0, Default: 1.0");
        }
        if (ImGui::SliderFloat("##FontShadowOffset", &fontShadowOffset, 0.0f, 5.0f, "%.1f")) {
            if (bridge) {
                bridge->setFontShadowOffset(fontShadowOffset);
                bridge->saveSettings();
            }
        }

        // Font Outline Size Setting
        static float fontOutlineSize = 1.5f;
        if (bridge) {
            try {
                fontOutlineSize = bridge->getFontOutlineSize();
            }
            catch (...) {
                // Keep current value if bridge access fails
            }
        }

        ImGui::Text("Font Outline Thickness:");
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
        if (ImGui::IsItemHovered()) {
            ImGui::SetTooltip("Adjust outline thickness\n0.0 = no outline, 5.0 = thick outline\nRange: 0.0-5.0, Default: 1.5");
        }
        if (ImGui::SliderFloat("##FontOutlineSize", &fontOutlineSize, 0.0f, 5.0f, "%.1f")) {
            if (bridge) {
                bridge->setFontOutlineSize(fontOutlineSize);
                bridge->saveSettings();
            }
        }

        ImGui::Separator();

            // Font Quality Settings
            ImGui::Text("Font Quality Settings:");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Advanced font rendering quality options (requires restart)");

            static int fontOversampleH = 8;
            static int fontOversampleV = 4;
            static float fontRasterizerMultiply = 1.0f;
            static bool fontPixelSnap = false;
            static int fontAtlasSize = 1024;

        if (bridge) {
            try {
                    fontOversampleH = bridge->getFontOversampleH();
                    fontOversampleV = bridge->getFontOversampleV();
                    fontRasterizerMultiply = bridge->getFontRasterizerMultiply();
                    fontPixelSnap = bridge->getFontPixelSnap();
                    fontAtlasSize = bridge->getFontAtlasSize();
            }
            catch (...) {
                    // Keep current values if bridge access fails
                }
            }

            // Horizontal Oversampling
            ImGui::Text("Horizontal Oversampling:");
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Higher values = smoother text but larger memory usage\nRange: 4-32, Default: 8");
            }
            if (ImGui::SliderInt("##FontOversampleH", &fontOversampleH, 4, 32)) {
            if (bridge) {
                    bridge->setFontOversampleH(fontOversampleH);
                bridge->saveSettings();
            }
        }

            // Vertical Oversampling
            ImGui::Text("Vertical Oversampling:");
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Higher values = smoother text but larger memory usage\nRange: 2-16, Default: 4");
            }
            if (ImGui::SliderInt("##FontOversampleV", &fontOversampleV, 2, 16)) {
            if (bridge) {
                    bridge->setFontOversampleV(fontOversampleV);
                bridge->saveSettings();
            }
        }

            // Rasterizer Multiply
            ImGui::Text("Font Brightness:");
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Adjust font brightness\n>1.0 = brighter, <1.0 = darker\nRange: 0.5-2.0, Default: 1.0");
            }
            if (ImGui::SliderFloat("##FontRasterizerMultiply", &fontRasterizerMultiply, 0.5f, 2.0f, "%.1f")) {
            if (bridge) {
                    bridge->setFontRasterizerMultiply(fontRasterizerMultiply);
                bridge->saveSettings();
            }
        }

            // Pixel Snap
            if (ImGui::Checkbox("Pixel Snap", &fontPixelSnap)) {
            if (bridge) {
                    bridge->setFontPixelSnap(fontPixelSnap);
                bridge->saveSettings();
            }
        }
        ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Enable pixel-perfect text alignment for crisp rendering");
            }

            // Font Atlas Size
            ImGui::Text("Font Atlas Size:");
        ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Texture size for font atlas\nLarger = better quality but more memory\nOptions: 1024, 2048, 4096");
            }
            static const char* atlasSizeItems[] = { "1024", "2048", "4096" };
            static int currentAtlasItem = 0;
            if (fontAtlasSize == 1024) currentAtlasItem = 0;
            else if (fontAtlasSize == 2048) currentAtlasItem = 1;
            else if (fontAtlasSize == 4096) currentAtlasItem = 2;

            if (ImGui::Combo("##FontAtlasSize", &currentAtlasItem, atlasSizeItems, IM_ARRAYSIZE(atlasSizeItems))) {
                int newAtlasSize = 1024;
                if (currentAtlasItem == 1) newAtlasSize = 2048;
                else if (currentAtlasItem == 2) newAtlasSize = 4096;
                
                if (bridge) {
                    bridge->setFontAtlasSize(newAtlasSize);
                    bridge->saveSettings();
                }
                fontAtlasSize = newAtlasSize;
            }

            // UI Bias
            ImGui::Text("UI Bias:");
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(?)");
            if (ImGui::IsItemHovered()) {
                ImGui::SetTooltip("Adjusts font positioning and alignment\nRange: -1.0 to 1.0, Default: 0.0\nNegative values move text up, positive values move text down");
            }

            static float fontUIBias = 0.0f;
            if (bridge) {
                try {
                    fontUIBias = bridge->getFontUIBias();
                }
                catch (...) {
                    // Keep current value if bridge access fails
                }
            }

            if (ImGui::SliderFloat("##FontUIBias", &fontUIBias, -1.0f, 1.0f, "%.2f")) {
                if (bridge) {
                    bridge->setFontUIBias(fontUIBias);
                    bridge->saveSettings();
                }
            }

                ImGui::Separator();
            ImGui::TextColored(ImVec4(1.0f, 0.8f, 0.0f, 1.0f), "Font selection and size changes require application restart to take effect.");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Selected font: %s (%.0fpx base size)", windowsFonts[selectedFontIndex].c_str(), baseFontSize);

            ImGui::EndTabItem();
        }

        // ESP Colors Tab - Rich Color Customization
        if (ImGui::BeginTabItem("ESP Colors", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 6;

            ImGui::Text("ESP Color Customization");
            ImGui::Separator();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Customize colors for all ESP elements");
            ImGui::Spacing();

            // Get current settings
            auto settings = bridge->getSettingsManager()->getSettings();

            // Create scrollable region for color settings
            if (ImGui::BeginChild("ColorSettings", ImVec2(0, -30), true)) {

                // ===== PLAYER ESP COLORS =====
                if (ImGui::CollapsingHeader("Player ESP Colors", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();

                    // Player Box Color
                    ImVec4 playerBoxColor = ImVec4(
                        settings.playerBoxColorR / 255.0f,
                        settings.playerBoxColorG / 255.0f,
                        settings.playerBoxColorB / 255.0f,
                        settings.playerBoxColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Player Box Color", (float*)&playerBoxColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.playerBoxColorR = (int)(playerBoxColor.x * 255);
                        mutableSettings.playerBoxColorG = (int)(playerBoxColor.y * 255);
                        mutableSettings.playerBoxColorB = (int)(playerBoxColor.z * 255);
                        mutableSettings.playerBoxColorA = (int)(playerBoxColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Player Text Color (Legacy - kept for compatibility)
                    ImVec4 playerTextColor = ImVec4(
                        settings.playerTextColorR / 255.0f,
                        settings.playerTextColorG / 255.0f,
                        settings.playerTextColorB / 255.0f,
                        settings.playerTextColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Player Text Color (Legacy)", (float*)&playerTextColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.playerTextColorR = (int)(playerTextColor.x * 255);
                        mutableSettings.playerTextColorG = (int)(playerTextColor.y * 255);
                        mutableSettings.playerTextColorB = (int)(playerTextColor.z * 255);
                        mutableSettings.playerTextColorA = (int)(playerTextColor.w * 255);
                        bridge->saveSettings();
                    }

                    ImGui::Separator();
                    ImGui::Text("Individual Player Text Colors:");

                    // Player Name Color
                    ImVec4 playerNameColor = ImVec4(
                        settings.playerNameColorR / 255.0f,
                        settings.playerNameColorG / 255.0f,
                        settings.playerNameColorB / 255.0f,
                        settings.playerNameColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Player Name Color", (float*)&playerNameColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.playerNameColorR = (int)(playerNameColor.x * 255);
                        mutableSettings.playerNameColorG = (int)(playerNameColor.y * 255);
                        mutableSettings.playerNameColorB = (int)(playerNameColor.z * 255);
                        mutableSettings.playerNameColorA = (int)(playerNameColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Player Distance Color
                    ImVec4 playerDistanceColor = ImVec4(
                        settings.playerDistanceColorR / 255.0f,
                        settings.playerDistanceColorG / 255.0f,
                        settings.playerDistanceColorB / 255.0f,
                        settings.playerDistanceColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Player Distance Color", (float*)&playerDistanceColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.playerDistanceColorR = (int)(playerDistanceColor.x * 255);
                        mutableSettings.playerDistanceColorG = (int)(playerDistanceColor.y * 255);
                        mutableSettings.playerDistanceColorB = (int)(playerDistanceColor.z * 255);
                        mutableSettings.playerDistanceColorA = (int)(playerDistanceColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Player Hand Item Color
                    ImVec4 playerHandColor = ImVec4(
                        settings.playerHandColorR / 255.0f,
                        settings.playerHandColorG / 255.0f,
                        settings.playerHandColorB / 255.0f,
                        settings.playerHandColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Player Hand Item Color", (float*)&playerHandColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.playerHandColorR = (int)(playerHandColor.x * 255);
                        mutableSettings.playerHandColorG = (int)(playerHandColor.y * 255);
                        mutableSettings.playerHandColorB = (int)(playerHandColor.z * 255);
                        mutableSettings.playerHandColorA = (int)(playerHandColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Player Skeleton Color
                    ImVec4 playerSkeletonColor = ImVec4(
                        settings.playerSkeletonColorR / 255.0f,
                        settings.playerSkeletonColorG / 255.0f,
                        settings.playerSkeletonColorB / 255.0f,
                        settings.playerSkeletonColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Player Skeleton Color", (float*)&playerSkeletonColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.playerSkeletonColorR = (int)(playerSkeletonColor.x * 255);
                        mutableSettings.playerSkeletonColorG = (int)(playerSkeletonColor.y * 255);
                        mutableSettings.playerSkeletonColorB = (int)(playerSkeletonColor.z * 255);
                        mutableSettings.playerSkeletonColorA = (int)(playerSkeletonColor.w * 255);
                        bridge->saveSettings();
                    }
                }

                // ===== ZOMBIE ESP COLORS =====
                if (ImGui::CollapsingHeader("Zombie ESP Colors", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();

                    // Zombie Box Color
                    ImVec4 zombieBoxColor = ImVec4(
                        settings.zombieBoxColorR / 255.0f,
                        settings.zombieBoxColorG / 255.0f,
                        settings.zombieBoxColorB / 255.0f,
                        settings.zombieBoxColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Zombie Box Color", (float*)&zombieBoxColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.zombieBoxColorR = (int)(zombieBoxColor.x * 255);
                        mutableSettings.zombieBoxColorG = (int)(zombieBoxColor.y * 255);
                        mutableSettings.zombieBoxColorB = (int)(zombieBoxColor.z * 255);
                        mutableSettings.zombieBoxColorA = (int)(zombieBoxColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Zombie Text Color
                    ImVec4 zombieTextColor = ImVec4(
                        settings.zombieTextColorR / 255.0f,
                        settings.zombieTextColorG / 255.0f,
                        settings.zombieTextColorB / 255.0f,
                        settings.zombieTextColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Zombie Text Color", (float*)&zombieTextColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.zombieTextColorR = (int)(zombieTextColor.x * 255);
                        mutableSettings.zombieTextColorG = (int)(zombieTextColor.y * 255);
                        mutableSettings.zombieTextColorB = (int)(zombieTextColor.z * 255);
                        mutableSettings.zombieTextColorA = (int)(zombieTextColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Zombie Skeleton Color
                    ImVec4 zombieSkeletonColor = ImVec4(
                        settings.zombieSkeletonColorR / 255.0f,
                        settings.zombieSkeletonColorG / 255.0f,
                        settings.zombieSkeletonColorB / 255.0f,
                        settings.zombieSkeletonColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Zombie Skeleton Color", (float*)&zombieSkeletonColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.zombieSkeletonColorR = (int)(zombieSkeletonColor.x * 255);
                        mutableSettings.zombieSkeletonColorG = (int)(zombieSkeletonColor.y * 255);
                        mutableSettings.zombieSkeletonColorB = (int)(zombieSkeletonColor.z * 255);
                        mutableSettings.zombieSkeletonColorA = (int)(zombieSkeletonColor.w * 255);
                        bridge->saveSettings();
                    }
                }

                // ===== ANIMAL ESP COLORS =====
                if (ImGui::CollapsingHeader("Animal ESP Colors")) {
                    ImGui::Spacing();

                    // Animal Box Color
                    ImVec4 animalBoxColor = ImVec4(
                        settings.animalBoxColorR / 255.0f,
                        settings.animalBoxColorG / 255.0f,
                        settings.animalBoxColorB / 255.0f,
                        settings.animalBoxColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Animal Box Color", (float*)&animalBoxColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.animalBoxColorR = (int)(animalBoxColor.x * 255);
                        mutableSettings.animalBoxColorG = (int)(animalBoxColor.y * 255);
                        mutableSettings.animalBoxColorB = (int)(animalBoxColor.z * 255);
                        mutableSettings.animalBoxColorA = (int)(animalBoxColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Animal Text Color
                    ImVec4 animalTextColor = ImVec4(
                        settings.animalTextColorR / 255.0f,
                        settings.animalTextColorG / 255.0f,
                        settings.animalTextColorB / 255.0f,
                        settings.animalTextColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Animal Text Color", (float*)&animalTextColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.animalTextColorR = (int)(animalTextColor.x * 255);
                        mutableSettings.animalTextColorG = (int)(animalTextColor.y * 255);
                        mutableSettings.animalTextColorB = (int)(animalTextColor.z * 255);
                        mutableSettings.animalTextColorA = (int)(animalTextColor.w * 255);
                        bridge->saveSettings();
                    }
                }

                // ===== DEAD PLAYER ESP COLORS =====
                if (ImGui::CollapsingHeader("Dead Player ESP Colors")) {
                    ImGui::Spacing();

                    // Dead Player Box Color
                    ImVec4 deadPlayerBoxColor = ImVec4(
                        settings.deadPlayerBoxColorR / 255.0f,
                        settings.deadPlayerBoxColorG / 255.0f,
                        settings.deadPlayerBoxColorB / 255.0f,
                        settings.deadPlayerBoxColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Dead Player Box Color", (float*)&deadPlayerBoxColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.deadPlayerBoxColorR = (int)(deadPlayerBoxColor.x * 255);
                        mutableSettings.deadPlayerBoxColorG = (int)(deadPlayerBoxColor.y * 255);
                        mutableSettings.deadPlayerBoxColorB = (int)(deadPlayerBoxColor.z * 255);
                        mutableSettings.deadPlayerBoxColorA = (int)(deadPlayerBoxColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Dead Player Text Color
                    ImVec4 deadPlayerTextColor = ImVec4(
                        settings.deadPlayerTextColorR / 255.0f,
                        settings.deadPlayerTextColorG / 255.0f,
                        settings.deadPlayerTextColorB / 255.0f,
                        settings.deadPlayerTextColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Dead Player Text Color", (float*)&deadPlayerTextColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.deadPlayerTextColorR = (int)(deadPlayerTextColor.x * 255);
                        mutableSettings.deadPlayerTextColorG = (int)(deadPlayerTextColor.y * 255);
                        mutableSettings.deadPlayerTextColorB = (int)(deadPlayerTextColor.z * 255);
                        mutableSettings.deadPlayerTextColorA = (int)(deadPlayerTextColor.w * 255);
                        bridge->saveSettings();
                    }
                }

                // ===== VEHICLE ESP COLORS =====
                if (ImGui::CollapsingHeader("Vehicle ESP Colors")) {
                    ImGui::Spacing();

                    // Vehicle Box Color
                    ImVec4 vehicleBoxColor = ImVec4(
                        settings.vehicleBoxColorR / 255.0f,
                        settings.vehicleBoxColorG / 255.0f,
                        settings.vehicleBoxColorB / 255.0f,
                        settings.vehicleBoxColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Vehicle Box Color", (float*)&vehicleBoxColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.vehicleBoxColorR = (int)(vehicleBoxColor.x * 255);
                        mutableSettings.vehicleBoxColorG = (int)(vehicleBoxColor.y * 255);
                        mutableSettings.vehicleBoxColorB = (int)(vehicleBoxColor.z * 255);
                        mutableSettings.vehicleBoxColorA = (int)(vehicleBoxColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Vehicle Text Color
                    ImVec4 vehicleTextColor = ImVec4(
                        settings.vehicleTextColorR / 255.0f,
                        settings.vehicleTextColorG / 255.0f,
                        settings.vehicleTextColorB / 255.0f,
                        settings.vehicleTextColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Vehicle Text Color", (float*)&vehicleTextColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.vehicleTextColorR = (int)(vehicleTextColor.x * 255);
                        mutableSettings.vehicleTextColorG = (int)(vehicleTextColor.y * 255);
                        mutableSettings.vehicleTextColorB = (int)(vehicleTextColor.z * 255);
                        mutableSettings.vehicleTextColorA = (int)(vehicleTextColor.w * 255);
                        bridge->saveSettings();
                    }
                }

                // ===== HEALTH BAR COLORS =====
                if (ImGui::CollapsingHeader("Health Bar Colors")) {
                    ImGui::Spacing();

                    // High Health Color
                    ImVec4 healthBarHighColor = ImVec4(
                        settings.healthBarHighColorR / 255.0f,
                        settings.healthBarHighColorG / 255.0f,
                        settings.healthBarHighColorB / 255.0f,
                        settings.healthBarHighColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("High Health Color", (float*)&healthBarHighColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.healthBarHighColorR = (int)(healthBarHighColor.x * 255);
                        mutableSettings.healthBarHighColorG = (int)(healthBarHighColor.y * 255);
                        mutableSettings.healthBarHighColorB = (int)(healthBarHighColor.z * 255);
                        mutableSettings.healthBarHighColorA = (int)(healthBarHighColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Medium Health Color
                    ImVec4 healthBarMediumColor = ImVec4(
                        settings.healthBarMediumColorR / 255.0f,
                        settings.healthBarMediumColorG / 255.0f,
                        settings.healthBarMediumColorB / 255.0f,
                        settings.healthBarMediumColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Medium Health Color", (float*)&healthBarMediumColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.healthBarMediumColorR = (int)(healthBarMediumColor.x * 255);
                        mutableSettings.healthBarMediumColorG = (int)(healthBarMediumColor.y * 255);
                        mutableSettings.healthBarMediumColorB = (int)(healthBarMediumColor.z * 255);
                        mutableSettings.healthBarMediumColorA = (int)(healthBarMediumColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Low Health Color
                    ImVec4 healthBarLowColor = ImVec4(
                        settings.healthBarLowColorR / 255.0f,
                        settings.healthBarLowColorG / 255.0f,
                        settings.healthBarLowColorB / 255.0f,
                        settings.healthBarLowColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Low Health Color", (float*)&healthBarLowColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.healthBarLowColorR = (int)(healthBarLowColor.x * 255);
                        mutableSettings.healthBarLowColorG = (int)(healthBarLowColor.y * 255);
                        mutableSettings.healthBarLowColorB = (int)(healthBarLowColor.z * 255);
                        mutableSettings.healthBarLowColorA = (int)(healthBarLowColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Health Bar Background Color
                    ImVec4 healthBarBackgroundColor = ImVec4(
                        settings.healthBarBackgroundColorR / 255.0f,
                        settings.healthBarBackgroundColorG / 255.0f,
                        settings.healthBarBackgroundColorB / 255.0f,
                        settings.healthBarBackgroundColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Health Bar Background", (float*)&healthBarBackgroundColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.healthBarBackgroundColorR = (int)(healthBarBackgroundColor.x * 255);
                        mutableSettings.healthBarBackgroundColorG = (int)(healthBarBackgroundColor.y * 255);
                        mutableSettings.healthBarBackgroundColorB = (int)(healthBarBackgroundColor.z * 255);
                        mutableSettings.healthBarBackgroundColorA = (int)(healthBarBackgroundColor.w * 255);
                        bridge->saveSettings();
                    }
                }

                // ===== CONTAINER CONTENTS ESP COLORS =====
                if (ImGui::CollapsingHeader("Container Contents ESP Colors")) {
                    ImGui::Spacing();

                    // Container Contents Color
                    ImVec4 containerContentsColor = ImVec4(
                        settings.containerContentsColorR / 255.0f,
                        settings.containerContentsColorG / 255.0f,
                        settings.containerContentsColorB / 255.0f,
                        settings.containerContentsColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Container Contents Color", (float*)&containerContentsColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.containerContentsColorR = (int)(containerContentsColor.x * 255);
                        mutableSettings.containerContentsColorG = (int)(containerContentsColor.y * 255);
                        mutableSettings.containerContentsColorB = (int)(containerContentsColor.z * 255);
                        mutableSettings.containerContentsColorA = (int)(containerContentsColor.w * 255);
                        bridge->saveSettings();
                    }

                    ImGui::Spacing();
                }

                // ===== LOOT CATEGORY ESP COLORS =====
                if (ImGui::CollapsingHeader("Loot Category ESP Colors", ImGuiTreeNodeFlags_DefaultOpen)) {
                    ImGui::Spacing();

                    // Weapon Color
                    ImVec4 weaponColor = ImVec4(
                        settings.weaponColorR / 255.0f,
                        settings.weaponColorG / 255.0f,
                        settings.weaponColorB / 255.0f,
                        settings.weaponColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Weapon Color", (float*)&weaponColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.weaponColorR = (int)(weaponColor.x * 255);
                        mutableSettings.weaponColorG = (int)(weaponColor.y * 255);
                        mutableSettings.weaponColorB = (int)(weaponColor.z * 255);
                        mutableSettings.weaponColorA = (int)(weaponColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Clothing Color
                    ImVec4 clothingColor = ImVec4(
                        settings.clothingColorR / 255.0f,
                        settings.clothingColorG / 255.0f,
                        settings.clothingColorB / 255.0f,
                        settings.clothingColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Clothing Color", (float*)&clothingColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.clothingColorR = (int)(clothingColor.x * 255);
                        mutableSettings.clothingColorG = (int)(clothingColor.y * 255);
                        mutableSettings.clothingColorB = (int)(clothingColor.z * 255);
                        mutableSettings.clothingColorA = (int)(clothingColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Backpack Color
                    ImVec4 backpackColor = ImVec4(
                        settings.backpackColorR / 255.0f,
                        settings.backpackColorG / 255.0f,
                        settings.backpackColorB / 255.0f,
                        settings.backpackColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Backpack Color", (float*)&backpackColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.backpackColorR = (int)(backpackColor.x * 255);
                        mutableSettings.backpackColorG = (int)(backpackColor.y * 255);
                        mutableSettings.backpackColorB = (int)(backpackColor.z * 255);
                        mutableSettings.backpackColorA = (int)(backpackColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Food Color
                    ImVec4 foodColor = ImVec4(
                        settings.foodColorR / 255.0f,
                        settings.foodColorG / 255.0f,
                        settings.foodColorB / 255.0f,
                        settings.foodColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Food Color", (float*)&foodColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.foodColorR = (int)(foodColor.x * 255);
                        mutableSettings.foodColorG = (int)(foodColor.y * 255);
                        mutableSettings.foodColorB = (int)(foodColor.z * 255);
                        mutableSettings.foodColorA = (int)(foodColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Ammo Color
                    ImVec4 ammoColor = ImVec4(
                        settings.ammoColorR / 255.0f,
                        settings.ammoColorG / 255.0f,
                        settings.ammoColorB / 255.0f,
                        settings.ammoColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Ammo Color", (float*)&ammoColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.ammoColorR = (int)(ammoColor.x * 255);
                        mutableSettings.ammoColorG = (int)(ammoColor.y * 255);
                        mutableSettings.ammoColorB = (int)(ammoColor.z * 255);
                        mutableSettings.ammoColorA = (int)(ammoColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Proxy Magazine Color
                    ImVec4 proxyMagazineColor = ImVec4(
                        settings.proxyMagazineColorR / 255.0f,
                        settings.proxyMagazineColorG / 255.0f,
                        settings.proxyMagazineColorB / 255.0f,
                        settings.proxyMagazineColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Proxy Magazine Color", (float*)&proxyMagazineColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.proxyMagazineColorR = (int)(proxyMagazineColor.x * 255);
                        mutableSettings.proxyMagazineColorG = (int)(proxyMagazineColor.y * 255);
                        mutableSettings.proxyMagazineColorB = (int)(proxyMagazineColor.z * 255);
                        mutableSettings.proxyMagazineColorA = (int)(proxyMagazineColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Optic Color
                    ImVec4 opticColor = ImVec4(
                        settings.opticColorR / 255.0f,
                        settings.opticColorG / 255.0f,
                        settings.opticColorB / 255.0f,
                        settings.opticColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Optic Color", (float*)&opticColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.opticColorR = (int)(opticColor.x * 255);
                        mutableSettings.opticColorG = (int)(opticColor.y * 255);
                        mutableSettings.opticColorB = (int)(opticColor.z * 255);
                        mutableSettings.opticColorA = (int)(opticColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Base Building Color
                    ImVec4 baseBuildingColor = ImVec4(
                        settings.baseBuildingColorR / 255.0f,
                        settings.baseBuildingColorG / 255.0f,
                        settings.baseBuildingColorB / 255.0f,
                        settings.baseBuildingColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Base Building Color", (float*)&baseBuildingColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.baseBuildingColorR = (int)(baseBuildingColor.x * 255);
                        mutableSettings.baseBuildingColorG = (int)(baseBuildingColor.y * 255);
                        mutableSettings.baseBuildingColorB = (int)(baseBuildingColor.z * 255);
                        mutableSettings.baseBuildingColorA = (int)(baseBuildingColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Melee Color
                    ImVec4 meleeColor = ImVec4(
                        settings.meleeColorR / 255.0f,
                        settings.meleeColorG / 255.0f,
                        settings.meleeColorB / 255.0f,
                        settings.meleeColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Melee Color", (float*)&meleeColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.meleeColorR = (int)(meleeColor.x * 255);
                        mutableSettings.meleeColorG = (int)(meleeColor.y * 255);
                        mutableSettings.meleeColorB = (int)(meleeColor.z * 255);
                        mutableSettings.meleeColorA = (int)(meleeColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Explosive Color
                    ImVec4 explosiveColor = ImVec4(
                        settings.explosiveColorR / 255.0f,
                        settings.explosiveColorG / 255.0f,
                        settings.explosiveColorB / 255.0f,
                        settings.explosiveColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Explosive Color", (float*)&explosiveColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.explosiveColorR = (int)(explosiveColor.x * 255);
                        mutableSettings.explosiveColorG = (int)(explosiveColor.y * 255);
                        mutableSettings.explosiveColorB = (int)(explosiveColor.z * 255);
                        mutableSettings.explosiveColorA = (int)(explosiveColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Container Color
                    ImVec4 containerColor = ImVec4(
                        settings.containerColorR / 255.0f,
                        settings.containerColorG / 255.0f,
                        settings.containerColorB / 255.0f,
                        settings.containerColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Container Color", (float*)&containerColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.containerColorR = (int)(containerColor.x * 255);
                        mutableSettings.containerColorG = (int)(containerColor.y * 255);
                        mutableSettings.containerColorB = (int)(containerColor.z * 255);
                        mutableSettings.containerColorA = (int)(containerColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Cooking Color
                    ImVec4 cookingColor = ImVec4(
                        settings.cookingColorR / 255.0f,
                        settings.cookingColorG / 255.0f,
                        settings.cookingColorB / 255.0f,
                        settings.cookingColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Cooking Color", (float*)&cookingColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.cookingColorR = (int)(cookingColor.x * 255);
                        mutableSettings.cookingColorG = (int)(cookingColor.y * 255);
                        mutableSettings.cookingColorB = (int)(cookingColor.z * 255);
                        mutableSettings.cookingColorA = (int)(cookingColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Camping Color
                    ImVec4 campingColor = ImVec4(
                        settings.campingColorR / 255.0f,
                        settings.campingColorG / 255.0f,
                        settings.campingColorB / 255.0f,
                        settings.campingColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Camping Color", (float*)&campingColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.campingColorR = (int)(campingColor.x * 255);
                        mutableSettings.campingColorG = (int)(campingColor.y * 255);
                        mutableSettings.campingColorB = (int)(campingColor.z * 255);
                        mutableSettings.campingColorA = (int)(campingColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Stash Color
                    ImVec4 stashColor = ImVec4(
                        settings.stashColorR / 255.0f,
                        settings.stashColorG / 255.0f,
                        settings.stashColorB / 255.0f,
                        settings.stashColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Stash Color", (float*)&stashColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.stashColorR = (int)(stashColor.x * 255);
                        mutableSettings.stashColorG = (int)(stashColor.y * 255);
                        mutableSettings.stashColorB = (int)(stashColor.z * 255);
                        mutableSettings.stashColorA = (int)(stashColor.w * 255);
                        bridge->saveSettings();
                    }

                    // Ground Item Color
                    ImVec4 groundItemColor = ImVec4(
                        settings.groundItemColorR / 255.0f,
                        settings.groundItemColorG / 255.0f,
                        settings.groundItemColorB / 255.0f,
                        settings.groundItemColorA / 255.0f
                    );
                    if (ImGui::ColorEdit4("Ground Item Color", (float*)&groundItemColor)) {
                        auto& mutableSettings = bridge->getSettingsManager()->getSettings();
                        mutableSettings.groundItemColorR = (int)(groundItemColor.x * 255);
                        mutableSettings.groundItemColorG = (int)(groundItemColor.y * 255);
                        mutableSettings.groundItemColorB = (int)(groundItemColor.z * 255);
                        mutableSettings.groundItemColorA = (int)(groundItemColor.w * 255);
                        bridge->saveSettings();
                    }

                    ImGui::Spacing();
                }

            }
            ImGui::EndChild();

            // Reset to defaults button
            ImGui::Separator();
            if (ImGui::Button("Reset to Defaults")) {
                auto& mutableSettings = bridge->getSettingsManager()->getSettings();

                // Reset Player Colors (Red box, White text/skeleton)
                mutableSettings.playerBoxColorR = 255; mutableSettings.playerBoxColorG = 0; mutableSettings.playerBoxColorB = 0; mutableSettings.playerBoxColorA = 255;
                mutableSettings.playerTextColorR = 255; mutableSettings.playerTextColorG = 255; mutableSettings.playerTextColorB = 255; mutableSettings.playerTextColorA = 255;
                mutableSettings.playerSkeletonColorR = 255; mutableSettings.playerSkeletonColorG = 255; mutableSettings.playerSkeletonColorB = 255; mutableSettings.playerSkeletonColorA = 255;

                // Reset Zombie Colors (Yellow box/text, Red skeleton)
                mutableSettings.zombieBoxColorR = 255; mutableSettings.zombieBoxColorG = 255; mutableSettings.zombieBoxColorB = 0; mutableSettings.zombieBoxColorA = 255;
                mutableSettings.zombieTextColorR = 255; mutableSettings.zombieTextColorG = 255; mutableSettings.zombieTextColorB = 0; mutableSettings.zombieTextColorA = 255;
                mutableSettings.zombieSkeletonColorR = 255; mutableSettings.zombieSkeletonColorG = 0; mutableSettings.zombieSkeletonColorB = 0; mutableSettings.zombieSkeletonColorA = 255;

                // Reset Animal Colors (Green)
                mutableSettings.animalBoxColorR = 0; mutableSettings.animalBoxColorG = 255; mutableSettings.animalBoxColorB = 0; mutableSettings.animalBoxColorA = 200;
                mutableSettings.animalTextColorR = 0; mutableSettings.animalTextColorG = 255; mutableSettings.animalTextColorB = 0; mutableSettings.animalTextColorA = 255;

                // Reset Dead Player Colors (Cyan)
                mutableSettings.deadPlayerBoxColorR = 0; mutableSettings.deadPlayerBoxColorG = 255; mutableSettings.deadPlayerBoxColorB = 255; mutableSettings.deadPlayerBoxColorA = 255;
                mutableSettings.deadPlayerTextColorR = 0; mutableSettings.deadPlayerTextColorG = 255; mutableSettings.deadPlayerTextColorB = 255; mutableSettings.deadPlayerTextColorA = 255;

                // Reset Vehicle Colors (Magenta)
                mutableSettings.vehicleBoxColorR = 255; mutableSettings.vehicleBoxColorG = 0; mutableSettings.vehicleBoxColorB = 245; mutableSettings.vehicleBoxColorA = 255;
                mutableSettings.vehicleTextColorR = 255; mutableSettings.vehicleTextColorG = 0; mutableSettings.vehicleTextColorB = 245; mutableSettings.vehicleTextColorA = 255;

                // Reset Health Bar Colors
                mutableSettings.healthBarHighColorR = 0; mutableSettings.healthBarHighColorG = 255; mutableSettings.healthBarHighColorB = 0; mutableSettings.healthBarHighColorA = 255; // Green
                mutableSettings.healthBarMediumColorR = 255; mutableSettings.healthBarMediumColorG = 255; mutableSettings.healthBarMediumColorB = 0; mutableSettings.healthBarMediumColorA = 255; // Yellow
                mutableSettings.healthBarLowColorR = 255; mutableSettings.healthBarLowColorG = 0; mutableSettings.healthBarLowColorB = 0; mutableSettings.healthBarLowColorA = 255; // Red
                mutableSettings.healthBarBackgroundColorR = 32; mutableSettings.healthBarBackgroundColorG = 32; mutableSettings.healthBarBackgroundColorB = 32; mutableSettings.healthBarBackgroundColorA = 180;

                // Reset Container Contents Colors (Light Blue)
                mutableSettings.containerContentsColorR = 200; mutableSettings.containerContentsColorG = 200; mutableSettings.containerContentsColorB = 255; mutableSettings.containerContentsColorA = 255;

                // Reset Loot Category Colors
                mutableSettings.weaponColorR = 255; mutableSettings.weaponColorG = 0; mutableSettings.weaponColorB = 100; mutableSettings.weaponColorA = 255; // Pink
                mutableSettings.clothingColorR = 255; mutableSettings.clothingColorG = 255; mutableSettings.clothingColorB = 0; mutableSettings.clothingColorA = 255; // Yellow
                mutableSettings.backpackColorR = 175; mutableSettings.backpackColorG = 255; mutableSettings.backpackColorB = 0; mutableSettings.backpackColorA = 255; // Light green
                mutableSettings.foodColorR = 50; mutableSettings.foodColorG = 140; mutableSettings.foodColorB = 50; mutableSettings.foodColorA = 255; // Dark green
                mutableSettings.ammoColorR = 255; mutableSettings.ammoColorG = 117; mutableSettings.ammoColorB = 50; mutableSettings.ammoColorA = 255; // Orange
                mutableSettings.proxyMagazineColorR = 255; mutableSettings.proxyMagazineColorG = 117; mutableSettings.proxyMagazineColorB = 50; mutableSettings.proxyMagazineColorA = 255; // Orange
                mutableSettings.opticColorR = 0; mutableSettings.opticColorG = 150; mutableSettings.opticColorB = 255; mutableSettings.opticColorA = 255; // Blue
                mutableSettings.baseBuildingColorR = 0; mutableSettings.baseBuildingColorG = 150; mutableSettings.baseBuildingColorB = 255; mutableSettings.baseBuildingColorA = 255; // Blue
                mutableSettings.meleeColorR = 0; mutableSettings.meleeColorG = 150; mutableSettings.meleeColorB = 255; mutableSettings.meleeColorA = 255; // Blue
                mutableSettings.explosiveColorR = 255; mutableSettings.explosiveColorG = 0; mutableSettings.explosiveColorB = 50; mutableSettings.explosiveColorA = 255; // Red-orange
                mutableSettings.containerColorR = 255; mutableSettings.containerColorG = 255; mutableSettings.containerColorB = 0; mutableSettings.containerColorA = 255; // Yellow
                mutableSettings.cookingColorR = 255; mutableSettings.cookingColorG = 165; mutableSettings.cookingColorB = 0; mutableSettings.cookingColorA = 255; // Orange
                mutableSettings.campingColorR = 0; mutableSettings.campingColorG = 255; mutableSettings.campingColorB = 255; mutableSettings.campingColorA = 255; // Cyan
                mutableSettings.stashColorR = 128; mutableSettings.stashColorG = 0; mutableSettings.stashColorB = 128; mutableSettings.stashColorA = 255; // Purple
                mutableSettings.groundItemColorR = 255; mutableSettings.groundItemColorG = 255; mutableSettings.groundItemColorB = 255; mutableSettings.groundItemColorA = 255; // White

                bridge->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Restore original ESP colors");

            ImGui::EndTabItem();
        }

        // Settings Tab (combining Settings + Performance)
        if (ImGui::BeginTabItem("Settings", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 7;

            // Settings Management Section
            ImGui::Text("Settings Management");
            ImGui::Separator();

            if (ImGui::Button("Save Settings")) {
                bridge->saveSettings();
                ImGui::OpenPopup("Settings Saved");
            }
            ImGui::SameLine();
            if (ImGui::Button("Load Settings")) {
                bridge->loadSettings();
                ImGui::OpenPopup("Settings Loaded");
            }

            // Popup notifications
            if (ImGui::BeginPopupModal("Settings Saved", NULL, ImGuiWindowFlags_AlwaysAutoResize)) {
                ImGui::Text("Settings have been saved successfully!");
                if (ImGui::Button("OK")) {
                    ImGui::CloseCurrentPopup();
                }
                ImGui::EndPopup();
            }

            if (ImGui::BeginPopupModal("Settings Loaded", NULL, ImGuiWindowFlags_AlwaysAutoResize)) {
                ImGui::Text("Settings have been loaded successfully!");
                if (ImGui::Button("OK")) {
                    ImGui::CloseCurrentPopup();
                }
                ImGui::EndPopup();
            }

            ImGui::Separator();
            ImGui::Text("Settings are automatically saved when the application closes.");
            ImGui::Text("Settings file: app_settings.ini");

            // Debug Settings Section
            ImGui::Separator();
            ImGui::Text("Debug Settings");
            ImGui::Separator();
            
            // Console Window Toggle
            static bool consoleEnabled = true; // Enable console by default for debugging
            if (ImGui::Checkbox("Enable Console Window", &consoleEnabled)) {
                if (consoleEnabled) {
                    // Show console
                    AllocConsole();
                    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
                    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
                    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
                    SetConsoleTitle(L"DayZ-DMA Debug Console");
                    printf("Debug console enabled.\n");
                } else {
                    // Hide console
                    printf("Debug console disabled.\n");
                    FreeConsole();
                }
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(Toggle debug console window)");

            // Performance Settings Section (combined)
            ImGui::Separator();
            ImGui::Separator();
        #if HAS_MULTICORE_CONFIG
            ImGui::Text("Performance Settings");
            ImGui::Separator();

            // System information
            ImGui::Text("Hardware Cores: %d", DayZ::g_multiCoreConfig.getHardwareCoreCount());

                    // Load Multi-Core settings from main settings if available
        static bool multiCoreSettingsLoaded = false;
        if (!multiCoreSettingsLoaded && this->bridge && this->bridge->getSettingsManager()) {
            const auto& settings = this->bridge->getSettingsManager()->getSettings();
            if (settings.multiCoreProfile >= 0 && settings.multiCoreProfile <= 4) {
                DayZ::g_multiCoreConfig.setProfile(static_cast<DayZ::MultiCoreConfig::ProfileType>(settings.multiCoreProfile));
                multiCoreSettingsLoaded = true;
            }
        }

        // Current profile display
        auto currentProfileType = DayZ::g_multiCoreConfig.getCurrentProfileType();
        const char* profileNames[] = {"AUTO_DETECT", "MANY_CORE", "MEDIUM_CORE", "STANDARD", "MINIMAL"};
        const char* currentProfileName = profileNames[currentProfileType];

        ImGui::Text("Current Profile: %s", currentProfileName);

            // Profile selection
            ImGui::Separator();
            ImGui::Text("Profile Selection:");

            static int selectedProfile = currentProfileType;
            if (ImGui::RadioButton("Auto-Detect (Recommended)", &selectedProfile, DayZ::MultiCoreConfig::AUTO_DETECT)) {
                DayZ::g_multiCoreConfig.setProfile(DayZ::MultiCoreConfig::AUTO_DETECT);
                // Save to main settings
                auto& settings = this->bridge->getSettingsManager()->getSettings();
                settings.multiCoreProfile = DayZ::MultiCoreConfig::AUTO_DETECT;
                this->bridge->getSettingsManager()->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Automatically selects optimal profile");

            if (ImGui::RadioButton("Many-Core (Ultra-Performance)", &selectedProfile, DayZ::MultiCoreConfig::MANY_CORE)) {
                DayZ::g_multiCoreConfig.setProfile(DayZ::MultiCoreConfig::MANY_CORE);
                // Save to main settings
                auto& settings = this->bridge->getSettingsManager()->getSettings();
                settings.multiCoreProfile = DayZ::MultiCoreConfig::MANY_CORE;
                this->bridge->getSettingsManager()->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "1ms near, 200 FPS camera (16+ cores)");

            if (ImGui::RadioButton("Medium-Core (High-Performance)", &selectedProfile, DayZ::MultiCoreConfig::MEDIUM_CORE)) {
                DayZ::g_multiCoreConfig.setProfile(DayZ::MultiCoreConfig::MEDIUM_CORE);
                // Save to main settings
                auto& settings = this->bridge->getSettingsManager()->getSettings();
                settings.multiCoreProfile = DayZ::MultiCoreConfig::MEDIUM_CORE;
                this->bridge->getSettingsManager()->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 1.0f, 1.0f), "2ms near, 144 FPS camera (8-15 cores)");

            if (ImGui::RadioButton("Standard (Balanced)", &selectedProfile, DayZ::MultiCoreConfig::STANDARD)) {
                DayZ::g_multiCoreConfig.setProfile(DayZ::MultiCoreConfig::STANDARD);
                // Save to main settings
                auto& settings = this->bridge->getSettingsManager()->getSettings();
                settings.multiCoreProfile = DayZ::MultiCoreConfig::STANDARD;
                this->bridge->getSettingsManager()->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 1.0f, 0.0f, 1.0f), "4ms near, 144 FPS camera (4-7 cores)");

            if (ImGui::RadioButton("Minimal (Conservative)", &selectedProfile, DayZ::MultiCoreConfig::MINIMAL)) {
                DayZ::g_multiCoreConfig.setProfile(DayZ::MultiCoreConfig::MINIMAL);
                // Save to main settings
                auto& settings = this->bridge->getSettingsManager()->getSettings();
                settings.multiCoreProfile = DayZ::MultiCoreConfig::MINIMAL;
                this->bridge->getSettingsManager()->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "7ms near, 100 FPS camera (<4 cores)");

            ImGui::Separator();

            // Current timing display
            auto profile = DayZ::g_multiCoreConfig.getCurrentProfile();
            ImGui::Text("Current Timing Configuration:");
            ImGui::Text("  Near Worker: %dms (Target: %d FPS)", profile.nearWorkerMs, 1000/profile.nearWorkerMs);
            ImGui::Text("  Camera: %dms (Target: %d FPS)", profile.cameraUpdateMs, 1000/profile.cameraUpdateMs);
            ImGui::Text("  Far Worker: %dms", profile.farWorkerMs);
            ImGui::Text("  Item Worker: %dms", profile.itemWorkerMs);
            ImGui::Text("  Slow Worker: %dms", profile.slowWorkerMs);
            ImGui::Text("  Scoreboard: %dms", profile.scoreboardMs);

            ImGui::Separator();

            // Graphics Settings
            ImGui::Text("Graphics Settings:");
            bool vSyncEnabled = this->bridge->getSettingsManager()->getSettings().vSyncEnabled;
            if (ImGui::Checkbox("V-Sync (Vertical Synchronization)", &vSyncEnabled)) {
                auto& settings = this->bridge->getSettingsManager()->getSettings();
                settings.vSyncEnabled = vSyncEnabled;
                this->bridge->getSettingsManager()->saveSettings();
            }
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), vSyncEnabled ? "(Limits FPS to monitor refresh rate)" : "(Unlimited FPS)");

            ImGui::Separator();

            // Note about FPS Counter Settings moved to ESP tab
            ImGui::Text("📝 FPS Counter Settings Moved");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "FPS counter font size and position settings have been moved to:");
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "ESP Tab > FPS Counter Settings");
            ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "This provides better organization and easier access to overlay controls.");

            ImGui::Separator();
            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "Settings are applied immediately and saved automatically.");
            #endif

            ImGui::EndTabItem();
        }

        // Crosshair Tab
        if (ImGui::BeginTabItem("Crosshair", nullptr, ImGuiTabItemFlags_None)) {
            selectedTab = 8;

            ImGui::Text("Crosshair Settings");
            ImGui::Separator();

            // Enable/Disable Crosshair
            bool crosshairEnabled = bridge->isCrosshairEnabled();
            if (ImGui::Checkbox("Enable Crosshair", &crosshairEnabled)) {
                bridge->setCrosshairEnabled(crosshairEnabled);
                bridge->saveSettings();
            }

            if (crosshairEnabled) {
                ImGui::Separator();
                
                // Position Settings
                ImGui::Text("Position Settings:");
                
                // Vertical Position Offset
                float crosshairVerticalOffset = bridge->getCrosshairVerticalOffset();
                if (ImGui::SliderFloat("Vertical Offset", &crosshairVerticalOffset, -100.0f, 100.0f, "%.1f pixels")) {
                    bridge->setCrosshairVerticalOffset(crosshairVerticalOffset);
                    bridge->saveSettings();
                }
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Adjust crosshair position vertically. Positive values move it down, negative values move it up.");
                }

                // Horizontal Position Offset
                float crosshairHorizontalOffset = bridge->getCrosshairHorizontalOffset();
                if (ImGui::SliderFloat("Horizontal Offset", &crosshairHorizontalOffset, -100.0f, 100.0f, "%.1f pixels")) {
                    bridge->setCrosshairHorizontalOffset(crosshairHorizontalOffset);
                    bridge->saveSettings();
                }
                if (ImGui::IsItemHovered()) {
                    ImGui::SetTooltip("Adjust crosshair position horizontally. Positive values move it right, negative values move it left.");
                }

                ImGui::Separator();

                // Basic Settings
                ImGui::Text("Basic Settings:");
                
                float crosshairSize = bridge->getCrosshairSize();
                if (ImGui::SliderFloat("Size", &crosshairSize, 1.0f, 50.0f, "%.1f")) {
                    bridge->setCrosshairSize(crosshairSize);
                    bridge->saveSettings();
                }

                float crosshairThickness = bridge->getCrosshairThickness();
                if (ImGui::SliderFloat("Thickness", &crosshairThickness, 0.5f, 10.0f, "%.1f")) {
                    bridge->setCrosshairThickness(crosshairThickness);
                    bridge->saveSettings();
                }

                float crosshairGap = bridge->getCrosshairGap();
                if (ImGui::SliderFloat("Gap", &crosshairGap, 0.0f, 30.0f, "%.1f")) {
                    bridge->setCrosshairGap(crosshairGap);
                    bridge->saveSettings();
                }

                float crosshairAlpha = bridge->getCrosshairAlpha();
                if (ImGui::SliderFloat("Alpha", &crosshairAlpha, 0.0f, 1.0f, "%.2f")) {
                    bridge->setCrosshairAlpha(crosshairAlpha);
                    bridge->saveSettings();
                }

                ImGui::Separator();

                // Outline Settings
                ImGui::Text("Outline Settings:");
                
                bool crosshairOutline = bridge->isCrosshairOutline();
                if (ImGui::Checkbox("Enable Outline", &crosshairOutline)) {
                    bridge->setCrosshairOutline(crosshairOutline);
                    bridge->saveSettings();
                }

                if (crosshairOutline) {
                    float crosshairOutlineThickness = bridge->getCrosshairOutlineThickness();
                    if (ImGui::SliderFloat("Outline Thickness", &crosshairOutlineThickness, 0.5f, 5.0f, "%.1f")) {
                        bridge->setCrosshairOutlineThickness(crosshairOutlineThickness);
                        bridge->saveSettings();
                    }
                }

                ImGui::Separator();

                // Center Dot Settings
                ImGui::Text("Center Dot Settings:");
                
                bool crosshairDot = bridge->isCrosshairDot();
                if (ImGui::Checkbox("Enable Center Dot", &crosshairDot)) {
                    bridge->setCrosshairDot(crosshairDot);
                    bridge->saveSettings();
                }

                if (crosshairDot) {
                    float crosshairDotSize = bridge->getCrosshairDotSize();
                    if (ImGui::SliderFloat("Dot Size", &crosshairDotSize, 0.5f, 10.0f, "%.1f")) {
                        bridge->setCrosshairDotSize(crosshairDotSize);
                        bridge->saveSettings();
                    }
                }

                ImGui::Separator();

                // Color Settings
                ImGui::Text("Color Settings:");

                // Main Crosshair Color
                ImVec4 crosshairColor = ImVec4(
                    bridge->getCrosshairColorR() / 255.0f,
                    bridge->getCrosshairColorG() / 255.0f,
                    bridge->getCrosshairColorB() / 255.0f,
                    bridge->getCrosshairColorA() / 255.0f
                );
                if (ImGui::ColorEdit4("Crosshair Color", (float*)&crosshairColor)) {
                    bridge->setCrosshairColorR((int)(crosshairColor.x * 255));
                    bridge->setCrosshairColorG((int)(crosshairColor.y * 255));
                    bridge->setCrosshairColorB((int)(crosshairColor.z * 255));
                    bridge->setCrosshairColorA((int)(crosshairColor.w * 255));
                    bridge->saveSettings();
                }

                // Outline Color (only show if outline is enabled)
                if (crosshairOutline) {
                    ImVec4 outlineColor = ImVec4(
                        bridge->getCrosshairOutlineColorR() / 255.0f,
                        bridge->getCrosshairOutlineColorG() / 255.0f,
                        bridge->getCrosshairOutlineColorB() / 255.0f,
                        bridge->getCrosshairOutlineColorA() / 255.0f
                    );
                    if (ImGui::ColorEdit4("Outline Color", (float*)&outlineColor)) {
                        bridge->setCrosshairOutlineColorR((int)(outlineColor.x * 255));
                        bridge->setCrosshairOutlineColorG((int)(outlineColor.y * 255));
                        bridge->setCrosshairOutlineColorB((int)(outlineColor.z * 255));
                        bridge->setCrosshairOutlineColorA((int)(outlineColor.w * 255));
                        bridge->saveSettings();
                    }
                }

                ImGui::Separator();

                // Preview
                ImGui::Text("Preview:");
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "The crosshair will appear in the center of your screen when enabled.");
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Settings are applied immediately and saved automatically.");
            }

            ImGui::EndTabItem();
        }

        ImGui::EndTabBar();
    }

    ImGui::End(); // close mainwindow

    // Create separate overlay window when enabled
    if (overlayEnabled && bridge && bridge->getOverlay()) {
        // Set up overlay window properties
        ImGuiWindowFlags overlayWindowFlags = ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoCollapse;
        
        if (overlayLocked) {
            overlayWindowFlags |= ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoResize;
        }
        
        if (!overlayLocked) {
            overlayWindowFlags |= ImGuiWindowFlags_NoBackground;
        }
        
        // Set window size and position
        ImGui::SetNextWindowSize(ImVec2((float)overlayWidth, (float)overlayHeight), ImGuiCond_FirstUseEver);
        
        if (resetPosition) {
            ImGui::SetNextWindowPos(ImVec2(0, 0), ImGuiCond_Always);
            resetPosition = false;
        }
        
        // Create the overlay window
        if (ImGui::Begin("ESP Overlay", &overlayEnabled, overlayWindowFlags)) {
            // Call the overlay's DrawOverlay method to render ESP elements
            bridge->getOverlay()->DrawOverlay();
        }
        ImGui::End();
    }

    //Radar Logic
    if (!bridge->getMapManager()->isMapSelected())
        return;
    auto gameMap = bridge->getMapManager()->getSelectedGameMap();
    auto allocator = bridge->getMapManager()->getSelectedAllocator();
    if (!gameMap || !allocator)
        return;

    RECT rect;
    GetWindowRect(hwnd, &rect);


    if (mTrans.mapZoom < 0) {
        float mapSize = fmaxf(allocator->getWidth(), allocator->getHeight());
        float screenSize = fminf(rect.right - rect.left, rect.bottom - rect.top);
        mTrans.mapZoom = screenSize / mapSize;
        mTrans.mapHeight = allocator->getHeight();
        mTrans.mapWidth = allocator->getWidth();
    }

    auto mousePos = ImGui::GetMousePos();
    static float lastMousePosX = mousePos.x;
    static float lastMousePosY = mousePos.y;

    if (ImGui::IsMouseDown(ImGuiMouseButton_Left) && !ImGui::GetIO().WantCaptureMouse) {
        mTrans.dragOffsetX += mousePos.x - lastMousePosX;
        mTrans.dragOffsetY += mousePos.y - lastMousePosY;
    }
    if (ImGui::GetIO().MouseWheel != 0.0f) {
        float oldZoom = mTrans.mapZoom;
        //Zoom in/out
        mTrans.mapZoom *= (1 + (ImGui::GetIO().MouseWheel * .05));
        if (mTrans.mapZoom < 0.01)
            mTrans.mapZoom = 0.01;

        //Zoom toward cursor
        float deltaX = (allocator->getWidth() * oldZoom) - (allocator->getWidth() * mTrans.mapZoom);
        float deltaY = (allocator->getHeight() * oldZoom) - (allocator->getHeight() * mTrans.mapZoom);

        float percX = (mousePos.x - rect.left - mTrans.dragOffsetX) / ((allocator->getWidth() * mTrans.mapZoom));
        float percY = (mousePos.y - rect.top - mTrans.dragOffsetY) / ((allocator->getHeight() * mTrans.mapZoom));

        mTrans.dragOffsetX += (deltaX * percX);
        mTrans.dragOffsetY += (deltaY * percY);
    }

    lastMousePosX = mousePos.x;
    lastMousePosY = mousePos.y;

    // **Update gepufferte Spielerposition vor Nutzung**
    g_mainPlayerScreenPosBuffered = g_mainPlayerScreenPos;

    // Falls Follow Player aktiviert ist, passe mTrans basierend auf der Fenstergre an
    if (followPlayerEnabled) {
        float winWidth = static_cast<float>(rect.right - rect.left);
        float winHeight = static_cast<float>(rect.bottom - rect.top);
        float centerX = winWidth / 2.0f;
        float centerY = winHeight / 2.0f;

        float desiredOffsetX = centerX - g_mainPlayerScreenPosBuffered.x;
        float desiredOffsetY = centerY - g_mainPlayerScreenPosBuffered.y;

        // Reduced threshold from 5.0f to 2.0f for more responsive updates
        const float threshold = 2.0f;
        // Increased smoothFactor from 0.5f to 0.8f for faster following
        float smoothFactor = 0.8f;

        if (fabs(desiredOffsetX - mTrans.dragOffsetX) > threshold) {
            mTrans.dragOffsetX = desiredOffsetX;
        }
        if (fabs(desiredOffsetY - mTrans.dragOffsetY) > threshold) {
            mTrans.dragOffsetY = desiredOffsetY;
        }
    }


    ImDrawList* fgDrawList = ImGui::GetBackgroundDrawList();
    fgDrawList->AddImage(
        allocator->getImage(),
        ImVec2(
            rect.left + mTrans.dragOffsetX,
            rect.top + mTrans.dragOffsetY
        ),
        ImVec2(
            rect.left + mTrans.dragOffsetX + (allocator->getWidth() * mTrans.mapZoom),
            rect.top + mTrans.dragOffsetY + (allocator->getHeight() * mTrans.mapZoom)
        )
    );
    this->bridge->getRadar()->DrawOverlay(bridge->getMapManager()->getSelectedGameMap().get(), mTrans);
    
}

DMARender::RenderWindow::RenderWindow()
{
    g_pd3dDevicePtr = new ID3D11Device*;
    this->bridge = std::shared_ptr<RenderBridge>(new RenderBridge(g_pd3dDevicePtr));
}

void DMARender::RenderWindow::_setResizeParams(UINT width, UINT height)
{
    this->g_ResizeWidth = width;
    this->g_ResizeHeight = height;
}

void DMARender::RenderWindow::initializeWindow()
{
    // Enable console for debug output
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    SetConsoleTitle(L"DayZ-DMA Debug Console");

    // Test console output immediately
    printf("=== DayZ-DMA Debug Console ===\n");
    printf("Console initialized successfully!\n");
    printf("Watching for player exclusion debug messages...\n");
    printf("=====================================\n");
    fflush(stdout);

    // Load settings again now that console is initialized (for debug output)
    printf("[DEBUG] Console ready, reloading settings to see debug output...\n");
    fflush(stdout);
    if (this->bridge) {
        this->bridge->loadSettings();
    }

    // Create application window
    ImGui_ImplWin32_EnableDpiAwareness();

    WNDCLASSEXW wc = { sizeof(wc), CS_CLASSDC, DMARender::WndProc, 0L, 0L, GetModuleHandle(nullptr), nullptr, nullptr, nullptr, nullptr, L"ImGui Example", nullptr };
    ::RegisterClassExW(&wc);
    hwnd = ::CreateWindowW(wc.lpszClassName, L"DayZ-DMA-Radar",  WS_OVERLAPPEDWINDOW, 0, 0, 1600, 900, nullptr, nullptr, wc.hInstance, nullptr);
    DMARender::hwndMap[hwnd] = this;

    // Initialize Direct3D
    if (!CreateDeviceD3D(hwnd))
    {
        CleanupDeviceD3D();
        ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
        return;
    }

    // Show the window
    ::ShowWindow(hwnd, SW_MAXIMIZE);
    ::UpdateWindow(hwnd);

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    CreateFonts();
    LoadFreeTypeFonts();  // Load enhanced FreeType fonts for ESP
    this->bridge->createFonts();
    ImGuiIO& io = ImGui::GetIO(); (void)io;

    // High-Performance ImGui Configuration (Option 2 Optimization)
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;     // Enable Keyboard Controls
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;      // Enable Gamepad Controls
    //io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;         // Enable Docking
    io.ConfigFlags |= ImGuiConfigFlags_ViewportsEnable;       // Enable Multi-Viewport / Platform Windows

    // Performance Optimizations for High-FPS Applications
    // Removed ImGuiConfigFlags_NoMouseCursorChange to fix cursor spinning issue
    io.ConfigWindowsResizeFromEdges = false;                  // Reduce CPU usage for window resizing
    io.ConfigDragClickToInputText = false;                    // Reduce input processing overhead
    io.ConfigMacOSXBehaviors = false;                         // Disable macOS-specific behaviors for performance

    // Memory and rendering optimizations
    io.BackendFlags |= ImGuiBackendFlags_RendererHasVtxOffset; // Enable vertex buffer offsets for efficiency

    // Frame rate optimization settings
    io.MouseDrawCursor = false;                               // Let OS handle cursor for better performance
    io.ConfigInputTrickleEventQueue = false;                  // Process all input immediately for responsiveness

    // Fix cursor spinning issue - force reset to normal cursor
    SetCursor(LoadCursor(NULL, IDC_ARROW));                   // Reset to normal arrow cursor
    ShowCursor(TRUE);                                         // Ensure cursor is visible

    std::cout << "[IMGUI] High-performance configuration applied for high-FPS rendering" << std::endl;


                                         // Setup Modern Red and Dark Grey Professional Theme
        ApplyRedDarkGreyTheme();

    // When viewports are enabled we tweak WindowRounding/WindowBg so platform windows can look identical to regular ones.
    ImGuiStyle& style = ImGui::GetStyle();
    if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable)
    {
        style.WindowRounding = 12.0f;  // Keep our Material Design rounded corners
        style.Colors[ImGuiCol_WindowBg].w = 1.0f;
    }

    // Performance-optimized style settings
    style.AntiAliasedLines = true;                            // Keep anti-aliasing for quality
    style.AntiAliasedLinesUseTex = false;                     // Disable texture-based AA for performance
    style.AntiAliasedFill = true;                             // Keep fill anti-aliasing

    // Reduce overdraw and rendering complexity
    style.Alpha = 1.0f;                                       // Full opacity to avoid blending overhead
    style.DisabledAlpha = 0.6f;                               // Reasonable disabled alpha
    style.WindowRounding = 12.0f;                             // Keep rounded corners but not excessive
    style.ChildRounding = 8.0f;                               // Moderate child rounding
    style.FrameRounding = 6.0f;                               // Moderate frame rounding
    style.PopupRounding = 8.0f;                               // Moderate popup rounding
    style.ScrollbarRounding = 6.0f;                           // Moderate scrollbar rounding
    style.GrabRounding = 4.0f;                                // Moderate grab rounding
    style.TabRounding = 6.0f;                                 // Moderate tab rounding

    std::cout << "[IMGUI] Performance-optimized style settings applied" << std::endl;

    // Memory optimization for ImGui
    OptimizeImGuiMemory();

    // Enable high-quality anti-aliasing for crisp fonts and smooth lines
    style.AntiAliasedLines = true;
    style.AntiAliasedLinesUseTex = true;
    style.AntiAliasedFill = true;
    style.CurveTessellationTol = 0.5f;

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(hwnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);
    *g_pd3dDevicePtr = g_pd3dDevice;

    // FREETYPE IMPLEMENTATION (Step 11 from UnknownCheats guide)
    // Configure FreeType for enhanced font rendering in DMA ESP application
#ifdef IMGUI_ENABLE_FREETYPE
    std::cout << "[INIT] ✅ FreeType is ENABLED - Configuring enhanced font rendering..." << std::endl;

    // Configure FreeType flags for high-quality ESP text rendering
    ImFontConfig cfg;
    cfg.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_ForceAutoHint;  // Force auto-hinting for better quality
    cfg.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_LightHinting;   // Light hinting for LCD displays
    cfg.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_LoadColor;      // Support color fonts
    cfg.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_Bitmap;         // Enable bitmap strikes
    cfg.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_NoHinting;      // Disable hinting for smoother curves
    cfg.OversampleH = 3;  // Higher oversampling for better quality
    cfg.OversampleV = 2;  // Higher oversampling for better quality
    cfg.PixelSnapH = true;
    cfg.RasterizerMultiply = 1.2f;  // Slightly bolder for better visibility

    std::cout << "[INIT] ✅ FreeType flags configured for ENHANCED ESP rendering with high-quality settings" << std::endl;

    // Apply enhanced font configuration for ESP text (24px as preferred)
    io.Fonts->AddFontDefault(&cfg);

    std::cout << "[INIT] ✅ FreeType default font loaded with enhanced settings" << std::endl;
#else
    std::cout << "[INIT] ❌ FreeType is DISABLED - Using standard STB font rendering" << std::endl;
    io.Fonts->AddFontDefault();
#endif

    // FUSER COMPATIBILITY: Configure font texture for crisp rendering without filtering
    // Disable texture filtering for pixel-perfect font rendering (helps with Fuser)
    ImGui::GetIO().Fonts->TexUvScale = ImVec2(1.0f, 1.0f);
    ImGui::GetIO().Fonts->TexUvWhitePixel = ImVec2(0.5f, 0.5f);




    // Our state
    bool show_demo_window = true;
    bool show_another_window = false;
    ImVec4 clear_color = ImVec4(0.0f, 0.0f, 0.0f, 0.00f);

    // Main loop
    bool done = false;
    while (!done)
    {
        // Poll and handle messages (inputs, window resize, etc.)
        // See the WndProc() function below for our to dispatch events to the Win32 backend.
        MSG msg;
        while (::PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE))
        {
            ::TranslateMessage(&msg);
            ::DispatchMessage(&msg);
            if (msg.message == WM_QUIT)
                done = true;
        }
        if (done)
            break;

        // Handle window resize (we don't resize directly in the WM_SIZE handler)
        if (g_ResizeWidth != 0 && g_ResizeHeight != 0)
        {
            CleanupRenderTarget();
            g_pSwapChain->ResizeBuffers(0, g_ResizeWidth, g_ResizeHeight, DXGI_FORMAT_UNKNOWN, 0);
            g_ResizeWidth = g_ResizeHeight = 0;
            CreateRenderTarget();
        }

        // Start the Dear ImGui frame with performance optimizations
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();

        // Performance optimization: Set frame rate target for consistent performance
        ImGuiIO& io = ImGui::GetIO();
        static auto lastFrameTime = std::chrono::high_resolution_clock::now();
        auto currentTime = std::chrono::high_resolution_clock::now();
        auto deltaTime = std::chrono::duration<float>(currentTime - lastFrameTime).count();
        lastFrameTime = currentTime;

        // Clamp delta time to prevent large jumps that can cause performance issues
        io.DeltaTime = (std::min)(deltaTime, 1.0f / 30.0f); // Cap at 30 FPS minimum for stability

        ImGui::NewFrame();

        // Process keybinds (Item ESP Toggle)
        if (this->bridge != nullptr) {
            static bool keyWasPressed = false;
            int toggleKey = this->bridge->getItemESPToggleKey();

            // Check if the toggle key is currently pressed
            bool keyIsPressed = (GetAsyncKeyState(toggleKey) & 0x8000) != 0;

            // Toggle on key press (not while held down)
            if (keyIsPressed && !keyWasPressed) {
                bool currentState = this->bridge->isItemESPEnabled();
                this->bridge->setItemESPEnabled(!currentState);
                this->bridge->saveSettings(); // Save the new state
            }

            keyWasPressed = keyIsPressed;
        }

        // Fix cursor spinning issue - periodic cursor reset
        static int cursorResetCounter = 0;
        if (++cursorResetCounter % 60 == 0) { // Reset every 60 frames (~1 second at 60 FPS)
            HCURSOR currentCursor = GetCursor();
            if (currentCursor == NULL || currentCursor == LoadCursor(NULL, IDC_WAIT) || currentCursor == LoadCursor(NULL, IDC_APPSTARTING)) {
                SetCursor(LoadCursor(NULL, IDC_ARROW)); // Force normal arrow cursor
            }
        }

        if (this->bridge != nullptr) {
            if (this->bridge->getOverlay() != nullptr) {
                this->drawOverlayHandler();
            }
        }

        // Optimized Rendering Pipeline
        ImGui::Render();

        // Performance optimization: Only clear if necessary
        static bool needsClear = true;
        if (needsClear) {
            const float clear_color_with_alpha[4] = { clear_color.x * clear_color.w, clear_color.y * clear_color.w, clear_color.z * clear_color.w, clear_color.w };
            g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
            g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color_with_alpha);
        }

        // Performance optimization: Check if we have draw data before rendering
        ImDrawData* drawData = ImGui::GetDrawData();
        if (drawData && drawData->Valid && drawData->CmdListsCount > 0) {
            ImGui_ImplDX11_RenderDrawData(drawData);
        }

        // Update and Render additional Platform Windows (optimized)
        if (io.ConfigFlags & ImGuiConfigFlags_ViewportsEnable)
        {
            // 144 Hz optimization: Update platform windows every frame for maximum responsiveness
            // Previous optimization was limiting to 72 Hz (144 ÷ 2 = 72)
            ImGui::UpdatePlatformWindows();
            ImGui::RenderPlatformWindowsDefault();
        }

        // Use V-Sync setting from performance tab
        bool vSyncEnabled = this->bridge->getSettingsManager()->getSettings().vSyncEnabled;
        g_pSwapChain->Present(vSyncEnabled ? 1 : 0, 0); // Present with/without vsync based on setting
    }

    // Save settings before cleanup
    if (this->bridge) {
        this->bridge->saveSettings();
    }

    // Cleanup
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();

    CleanupDeviceD3D();
    ::DestroyWindow(hwnd);
    ::UnregisterClassW(wc.lpszClassName, wc.hInstance);
    return;
}

std::shared_ptr<DMARender::RenderBridge> DMARender::RenderWindow::getBridge()
{
    return this->bridge;
}

// Helper functions
bool DMARender::RenderWindow::CreateDeviceD3D(HWND hWnd)
{
    // Setup swap chain
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 3;  // Triple buffering for smoother high FPS performance
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_B8G8R8A8_UNORM;  // BGRA format - better compatibility with overlay capture software
    sd.BufferDesc.RefreshRate.Numerator = 0;   // Auto-detect monitor refresh rate
    sd.BufferDesc.RefreshRate.Denominator = 0; // Auto-detect monitor refresh rate
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = hWnd;
    // Enable MSAA for better font rendering with Fuser overlay capture
    sd.SampleDesc.Count = 4;     // 4x MSAA for smooth font edges
    sd.SampleDesc.Quality = 0;   // Use highest quality available
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_FLIP_DISCARD;  // Modern flip model - better for overlay capture

    UINT createDeviceFlags = 0;
    //createDeviceFlags |= D3D11_CREATE_DEVICE_DEBUG;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };

    // First create device to check MSAA support
    ID3D11Device* tempDevice = nullptr;
    ID3D11DeviceContext* tempContext = nullptr;
    HRESULT tempRes = D3D11CreateDevice(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &tempDevice, &featureLevel, &tempContext);

    if (SUCCEEDED(tempRes) && tempDevice) {
        // Check for 4x MSAA quality support
        UINT qualityLevels = 0;
        tempDevice->CheckMultisampleQualityLevels(DXGI_FORMAT_B8G8R8A8_UNORM, 4, &qualityLevels);
        if (qualityLevels > 0) {
            sd.SampleDesc.Quality = qualityLevels - 1; // Use highest quality
        }
        tempDevice->Release();
        tempContext->Release();
    }

    HRESULT res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);

    // If 4x MSAA fails, try 2x MSAA
    if (res == DXGI_ERROR_INVALID_CALL && sd.SampleDesc.Count == 4) {
        sd.SampleDesc.Count = 2;
        sd.SampleDesc.Quality = 0;
        res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    }

    // If 2x MSAA fails, fallback to no MSAA
    if (res == DXGI_ERROR_INVALID_CALL && sd.SampleDesc.Count == 2) {
        sd.SampleDesc.Count = 1;
        sd.SampleDesc.Quality = 0;
        res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    }

    if (res == DXGI_ERROR_UNSUPPORTED) // Try high-performance WARP software driver if hardware is not available.
        res = D3D11CreateDeviceAndSwapChain(nullptr, D3D_DRIVER_TYPE_WARP, nullptr, createDeviceFlags, featureLevelArray, 2, D3D11_SDK_VERSION, &sd, &g_pSwapChain, &g_pd3dDevice, &featureLevel, &g_pd3dDeviceContext);
    if (res != S_OK)
        return false;

    CreateRenderTarget();
    return true;
}

void DMARender::RenderWindow::CleanupDeviceD3D()
{
    // Enhanced DirectX device cleanup with proper order and validation
    std::cout << "[DIRECTX] Starting DirectX device cleanup..." << std::endl;

    // Clean up render targets first
    CleanupRenderTarget();

    // Clean up swap chain
    if (g_pSwapChain) {
        // Set windowed mode before releasing swap chain (recommended practice)
        g_pSwapChain->SetFullscreenState(FALSE, nullptr);
        g_pSwapChain->Release();
        g_pSwapChain = nullptr;
        std::cout << "[DIRECTX] Swap chain released" << std::endl;
    }

    // Clean up device context
    if (g_pd3dDeviceContext) {
        // Clear any bound resources before releasing context
        g_pd3dDeviceContext->ClearState();
        g_pd3dDeviceContext->Flush();
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = nullptr;
        std::cout << "[DIRECTX] Device context released" << std::endl;
    }

    // Clean up device last
    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = nullptr;
        std::cout << "[DIRECTX] Device released" << std::endl;
    }

    std::cout << "[DIRECTX] DirectX device cleanup completed successfully" << std::endl;
}

bool DMARender::RenderWindow::ValidateRenderTarget()
{
    // Validate render target health and configuration
    if (!g_mainRenderTargetView) {
        std::cout << "[DIRECTX WARNING] Main render target view is null" << std::endl;
        return false;
    }

    if (!g_pSwapChain) {
        std::cout << "[DIRECTX WARNING] Swap chain is null during render target validation" << std::endl;
        return false;
    }

    // Get swap chain description for validation
    DXGI_SWAP_CHAIN_DESC swapChainDesc;
    HRESULT hr = g_pSwapChain->GetDesc(&swapChainDesc);
    if (FAILED(hr)) {
        std::cout << "[DIRECTX WARNING] Failed to get swap chain description for validation" << std::endl;
        return false;
    }

    // Validate that we have the expected configuration
    if (swapChainDesc.BufferCount != 3) {
        std::cout << "[DIRECTX WARNING] Unexpected buffer count: " << swapChainDesc.BufferCount << " (expected 3)" << std::endl;
    }

    std::cout << "[DIRECTX] Render target validation passed" << std::endl;
    return true;
}

void DMARender::RenderWindow::OptimizeImGuiMemory()
{
    // ImGui Memory and Performance Optimizations
    ImGuiIO& io = ImGui::GetIO();

    // Optimize font atlas for better performance
    io.Fonts->Flags |= ImFontAtlasFlags_NoPowerOfTwoHeight;    // Allow non-power-of-2 heights for memory efficiency
    io.Fonts->Flags |= ImFontAtlasFlags_NoMouseCursors;       // Don't build mouse cursor textures

    // Set reasonable limits for memory usage
    io.MetricsRenderVertices = 0;                             // Reset vertex metrics
    io.MetricsRenderIndices = 0;                              // Reset index metrics
    io.MetricsRenderWindows = 0;                              // Reset window metrics

    // Additional performance settings
    io.ConfigMemoryCompactTimer = 60.0f;                      // Compact memory every 60 seconds

    std::cout << "[IMGUI] Memory optimizations applied for high-FPS performance" << std::endl;
}

void DMARender::RenderWindow::CreateRenderTarget()
{
    // Enhanced render target creation with better error handling and validation
    ID3D11Texture2D* pBackBuffer = nullptr;
    HRESULT hr = g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));

    if (FAILED(hr)) {
        std::cout << "[DIRECTX ERROR] Failed to get swap chain back buffer. HRESULT: 0x" << std::hex << hr << std::endl;
        return;
    }

    if (pBackBuffer == nullptr) {
        std::cout << "[DIRECTX ERROR] Back buffer is null after successful GetBuffer call" << std::endl;
        return;
    }

    // Get back buffer description for validation and logging
    D3D11_TEXTURE2D_DESC backBufferDesc;
    pBackBuffer->GetDesc(&backBufferDesc);

    // Create render target view with enhanced configuration
    D3D11_RENDER_TARGET_VIEW_DESC rtvDesc = {};
    rtvDesc.Format = backBufferDesc.Format;
    rtvDesc.ViewDimension = (backBufferDesc.SampleDesc.Count > 1) ? D3D11_RTV_DIMENSION_TEXTURE2DMS : D3D11_RTV_DIMENSION_TEXTURE2D;
    rtvDesc.Texture2D.MipSlice = 0;

    hr = g_pd3dDevice->CreateRenderTargetView(pBackBuffer, &rtvDesc, &g_mainRenderTargetView);

    if (FAILED(hr)) {
        std::cout << "[DIRECTX ERROR] Failed to create render target view. HRESULT: 0x" << std::hex << hr << std::endl;
        pBackBuffer->Release();
        return;
    }

    // Log successful render target creation with details
    std::cout << "[DIRECTX] Render target created successfully:" << std::endl;
    std::cout << "  - Resolution: " << backBufferDesc.Width << "x" << backBufferDesc.Height << std::endl;
    std::cout << "  - Format: " << backBufferDesc.Format << std::endl;
    std::cout << "  - MSAA: " << backBufferDesc.SampleDesc.Count << "x" << std::endl;
    std::cout << "  - View Dimension: " << ((backBufferDesc.SampleDesc.Count > 1) ? "TEXTURE2DMS" : "TEXTURE2D") << std::endl;

    pBackBuffer->Release();
}

void DMARender::RenderWindow::CleanupRenderTarget()
{
    // Enhanced render target cleanup with validation and logging
    if (g_mainRenderTargetView) {
        // Get reference count before release for debugging
        ULONG refCount = g_mainRenderTargetView->AddRef();
        g_mainRenderTargetView->Release(); // Remove the AddRef we just did

        if (refCount > 1) {
            std::cout << "[DIRECTX WARNING] Render target view has " << (refCount - 1) << " additional references before cleanup" << std::endl;
        }

        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
        std::cout << "[DIRECTX] Render target view cleaned up successfully" << std::endl;
    } else {
        std::cout << "[DIRECTX] Render target view was already null during cleanup" << std::endl;
    }
}

void DMARender::RenderWindow::CreateFonts()
{
    // QUALITY-ENHANCED font configuration for window identification
    ImGuiIO& io = ImGui::GetIO();
    io.Fonts->TexGlyphPadding = 2; // Increased padding to prevent bleeding with Fuser
    io.Fonts->Flags |= ImFontAtlasFlags_NoPowerOfTwoHeight; // Allow non-power-of-2 for better packing
    io.Fonts->Flags |= ImFontAtlasFlags_NoMouseCursors; // Optimize atlas for text only
    io.Fonts->Flags |= ImFontAtlasFlags_NoBakedLines; // Disable baked lines for cleaner rendering with Fuser

    // Load Consolas font for main UI instead of default ImGui font
    ImFontConfig mainUIConfig;
    mainUIConfig.OversampleH = 2;
    mainUIConfig.OversampleV = 1;
    mainUIConfig.PixelSnapH = true;
    mainUIConfig.RasterizerMultiply = 1.0f;
    mainUIConfig.FontDataOwnedByAtlas = false;

    // Try to load Consolas for main UI
    ImFont* consolasFont = nullptr;
    std::vector<std::string> consolasPaths = {
        "C:\\Windows\\Fonts\\consola.ttf",
        "C:\\Windows\\Fonts\\Consolas.ttf",
        "C:\\Windows\\Fonts\\consolas.ttf"
    };

    for (const auto& consolasPath : consolasPaths) {
        try {
            std::cout << "[MAIN UI FONT] Attempting to load Consolas: " << consolasPath << std::endl;
            consolasFont = io.Fonts->AddFontFromFileTTF(consolasPath.c_str(), 14.0f, &mainUIConfig);
            if (consolasFont) {
                std::cout << "[MAIN UI FONT] Successfully loaded Consolas from: " << consolasPath << std::endl;
                break;
            }
        }
        catch (...) {
            std::cout << "[MAIN UI FONT] Failed to load Consolas from: " << consolasPath << std::endl;
            continue;
        }
    }

    // Fallback to default if Consolas loading failed
    if (!consolasFont) {
        std::cout << "[MAIN UI FONT] Consolas not found, using ImGui default font" << std::endl;
        ImGui::GetIO().Fonts->AddFontDefault();
    }

    ImFontConfig config;
    
    // Get font quality settings from bridge (if available) with CRASH PROTECTION
    int fontOversampleH = 2;    // Safe default values
    int fontOversampleV = 1;
    float fontRasterizerMultiply = 1.0f;
    bool fontPixelSnap = true;
    int fontAtlasSize = 1024;
    
    if (bridge) {
        try {
            int bridgeOversampleH = bridge->getFontOversampleH();
            int bridgeOversampleV = bridge->getFontOversampleV();
            float bridgeRasterizerMultiply = bridge->getFontRasterizerMultiply();
            bool bridgePixelSnap = bridge->getFontPixelSnap();
            int bridgeAtlasSize = bridge->getFontAtlasSize();
            
            // CRASH PROTECTION: Validate all values before using them
            if (bridgeOversampleH >= 1 && bridgeOversampleH <= 8) {
                fontOversampleH = bridgeOversampleH;
            } else {
                std::cout << "[FONT SAFETY] Invalid OversampleH " << bridgeOversampleH << ", using safe default " << fontOversampleH << std::endl;
            }
            
            if (bridgeOversampleV >= 1 && bridgeOversampleV <= 4) {
                fontOversampleV = bridgeOversampleV;
            } else {
                std::cout << "[FONT SAFETY] Invalid OversampleV " << bridgeOversampleV << ", using safe default " << fontOversampleV << std::endl;
            }
            
            if (bridgeRasterizerMultiply >= 0.8f && bridgeRasterizerMultiply <= 1.5f) {
                fontRasterizerMultiply = bridgeRasterizerMultiply;
            } else {
                std::cout << "[FONT SAFETY] Invalid RasterizerMultiply " << bridgeRasterizerMultiply << ", using safe default " << fontRasterizerMultiply << std::endl;
            }
            
            fontPixelSnap = bridgePixelSnap; // Boolean is always safe
            
            if (bridgeAtlasSize == 1024 || bridgeAtlasSize == 2048) { // Restrict to safer sizes only
                fontAtlasSize = bridgeAtlasSize;
            } else {
                std::cout << "[FONT SAFETY] Invalid AtlasSize " << bridgeAtlasSize << ", using safe default " << fontAtlasSize << std::endl;
            }
            
            std::cout << "[FONT QUALITY] SAFE settings applied: OversampleH=" << fontOversampleH 
                      << ", OversampleV=" << fontOversampleV 
                      << ", RasterizerMultiply=" << fontRasterizerMultiply 
                      << ", PixelSnap=" << (fontPixelSnap ? "true" : "false")
                      << ", AtlasSize=" << fontAtlasSize << std::endl;
        }
        catch (...) {
            std::cout << "[FONT SAFETY] Exception getting font quality settings, using safe defaults" << std::endl;
        }
    }
    
    // Apply the VALIDATED font quality settings
    config.OversampleH = fontOversampleH;
    config.OversampleV = fontOversampleV;
    config.GlyphExtraSpacing.x = 0.0f; // Natural spacing for better readability
    config.PixelSnapH = fontPixelSnap;
    config.RasterizerMultiply = fontRasterizerMultiply;
    config.FontDataOwnedByAtlas = false; // Better memory management for quality
    
    // Add Cyrillic support to fix ?????? issue with Russian/Cyrillic text
    config.GlyphRanges = ImGui::GetIO().Fonts->GetGlyphRangesCyrillic();
    
    // Apply validated atlas size setting
    io.Fonts->TexDesiredWidth = fontAtlasSize;
    
    // DYNAMIC FONT SELECTION for Window UI: Use same font as ESP for consistency
    std::string selectedFontFile = "arial.ttf"; // Default fallback
    if (bridge) {
        try {
            selectedFontFile = bridge->getSelectedFontFile();
            std::cout << "[WINDOW FONT] Using selected font: " << selectedFontFile << std::endl;
        }
        catch (...) {
            std::cout << "[WINDOW FONT] Failed to get selected font, using Arial fallback" << std::endl;
        }
    }
    
    // Build window font paths with fallbacks
    std::vector<std::string> windowFontPaths;
    
    // Try to get the full path to the selected font first
    if (bridge) {
        try {
            std::string selectedFontPath = bridge->getSelectedFontFullPath();
            windowFontPaths.push_back(selectedFontPath);
            std::cout << "[WINDOW FONT] Using selected font path: " << selectedFontPath << std::endl;
        }
        catch (...) {
            // Fallback to Windows Fonts directory if the new method fails
            windowFontPaths.push_back("C:\\Windows\\Fonts\\" + selectedFontFile);
            std::cout << "[WINDOW FONT] Failed to get full path, using fallback: C:\\Windows\\Fonts\\" << selectedFontFile << std::endl;
        }
    } else {
        windowFontPaths.push_back("C:\\Windows\\Fonts\\" + selectedFontFile);
    }
    
    // Add essential fallback fonts
    windowFontPaths.insert(windowFontPaths.end(), {
        "C:\\Windows\\Fonts\\arial.ttf",
        "C:\\Windows\\Fonts\\calibri.ttf", 
        "C:\\Windows\\Fonts\\tahoma.ttf"
    });
    
    // Try to load window font with fallbacks and crash protection
    windowIdentifyFont = nullptr;
    for (const auto& fontPath : windowFontPaths) {
        try {
            std::cout << "[WINDOW FONT] Attempting to load window font: " << fontPath << std::endl;
            
            // Validate the font file first if we have bridge access
            bool fontIsValid = true;
            if (bridge) {
                try {
                    fontIsValid = bridge->isValidFontFile(fontPath);
                    if (!fontIsValid) {
                        std::cout << "[WINDOW FONT] Font validation failed, skipping: " << fontPath << std::endl;
                        continue;
                    }
                }
                catch (...) {
                    std::cout << "[WINDOW FONT] Font validation threw exception, skipping: " << fontPath << std::endl;
                    continue;
                }
            }
            
            // CRASH PROTECTION: Use safer font size and additional validation
            float safeFontSize = 16.0f; // Much safer size than 256
            std::cout << "[WINDOW FONT] Attempting to load with safe size " << safeFontSize << "px" << std::endl;
            
            ImFont* tempFont = io.Fonts->AddFontFromFileTTF(fontPath.c_str(), safeFontSize, &config);
            if (tempFont) {
                windowIdentifyFont = tempFont;
                std::cout << "[WINDOW FONT] Successfully loaded window font: " << fontPath << std::endl;
                break;
            } else {
                std::cout << "[WINDOW FONT] Font loading returned null: " << fontPath << std::endl;
            }
        }
        catch (const std::exception& e) {
            std::cout << "[WINDOW FONT] Exception loading font " << fontPath << ": " << e.what() << std::endl;
            continue;
        }
        catch (...) {
            std::cout << "[WINDOW FONT] Unknown exception loading font: " << fontPath << std::endl;
            continue;
        }
    }
    
    // Fallback to default if all font loading failed
    if (!windowIdentifyFont) {
        std::cout << "[WINDOW FONT] All font loading failed, attempting default font" << std::endl;
        try {
            windowIdentifyFont = io.Fonts->AddFontDefault();
            std::cout << "[WINDOW FONT] Successfully loaded default window font" << std::endl;
        }
        catch (...) {
            std::cout << "[WINDOW FONT] CRITICAL: Even default font failed to load!" << std::endl;
        }
    }
    
    // CRASH PROTECTION: Validate font atlas build
    try {
        std::cout << "[WINDOW FONT] Building font atlas with SAFE settings..." << std::endl;
        if (!io.Fonts->Build()) {
            std::cout << "[WINDOW FONT] Font atlas build failed, clearing and using minimal setup" << std::endl;
            io.Fonts->Clear();
            windowIdentifyFont = io.Fonts->AddFontDefault();
            io.Fonts->Build();
        } else {
            std::cout << "[WINDOW FONT] Font atlas built successfully" << std::endl;
        }
    }
    catch (...) {
        std::cout << "[WINDOW FONT] CRITICAL: Font atlas build crashed, using emergency fallback" << std::endl;
        try {
            io.Fonts->Clear();
            windowIdentifyFont = io.Fonts->AddFontDefault();
            io.Fonts->Build();
        }
        catch (...) {
            std::cout << "[WINDOW FONT] EMERGENCY: Complete font system failure!" << std::endl;
        }
    }
}

void DMARender::RenderWindow::LoadFreeTypeFonts()
{
#ifdef IMGUI_ENABLE_FREETYPE
    std::cout << "[FREETYPE] ✅ FreeType is ENABLED - Loading enhanced fonts for DMA ESP application..." << std::endl;
    std::cout << "[FREETYPE] ✅ Using FreeType for high-quality font rendering" << std::endl;

    ImGuiIO& io = ImGui::GetIO();

    // Configure FreeType for ESP text (24px as preferred) with MAXIMUM QUALITY
    ImFontConfig espConfig;
    espConfig.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_ForceAutoHint;  // Force auto-hinting
    espConfig.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_LightHinting;   // Light hinting for LCD
    espConfig.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_LoadColor;      // Support color fonts
    espConfig.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_Bitmap;         // Enable bitmap strikes
    espConfig.FontBuilderFlags |= ImGuiFreeTypeBuilderFlags_NoHinting;      // Disable hinting for smoother curves
    espConfig.OversampleH = 4;  // MAXIMUM oversampling for crystal clear text
    espConfig.OversampleV = 4;  // MAXIMUM oversampling for crystal clear text
    espConfig.PixelSnapH = true;
    espConfig.RasterizerMultiply = 1.3f;  // Make text bolder for better visibility
    espConfig.FontDataOwnedByAtlas = false;
    
    // Add Cyrillic support to fix ?????? issue with Russian/Cyrillic text
    espConfig.GlyphRanges = ImGui::GetIO().Fonts->GetGlyphRangesCyrillic();

    std::cout << "[FREETYPE] ✅ FreeType MAXIMUM QUALITY flags configured: 4x4 oversampling + enhanced hinting" << std::endl;

    // Load ESP font (24px for player names, distances)
    std::vector<std::string> espFontPaths = {
        "C:\\Windows\\Fonts\\arial.ttf",
        "C:\\Windows\\Fonts\\calibri.ttf",
        "C:\\Windows\\Fonts\\tahoma.ttf"
    };

    for (const auto& fontPath : espFontPaths) {
        try {
            std::cout << "[FREETYPE] Attempting to load ESP font: " << fontPath << std::endl;
            playerFont = io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 24.0f, &espConfig);
            if (playerFont) {
                std::cout << "[FREETYPE] Successfully loaded ESP font: " << fontPath << std::endl;
                break;
            }
        }
        catch (...) {
            std::cout << "[FREETYPE] Failed to load ESP font: " << fontPath << std::endl;
            continue;
        }
    }

    // Fallback if ESP font loading failed
    if (!playerFont) {
        std::cout << "[FREETYPE] ESP font loading failed, using default with FreeType flags" << std::endl;
        playerFont = io.Fonts->AddFontDefault(&espConfig);
    }

    // Build the font atlas with FreeType
    if (!io.Fonts->Build()) {
        std::cout << "[FREETYPE] ❌ Font atlas build failed!" << std::endl;
    } else {
        std::cout << "[FREETYPE] ✅ Font atlas built successfully with FreeType enhanced rendering" << std::endl;
        std::cout << "[FREETYPE] ✅ Atlas size: " << io.Fonts->TexWidth << "x" << io.Fonts->TexHeight << std::endl;
        std::cout << "[FREETYPE] ✅ Font count: " << io.Fonts->Fonts.Size << std::endl;
    }

    std::cout << "[FREETYPE] ✅ Enhanced font loading complete for DMA ESP - FreeType is ACTIVE!" << std::endl;
#else
    std::cout << "[FREETYPE] ❌ FreeType NOT ENABLED - using standard STB font loading" << std::endl;
#endif
}

#ifndef WM_DPICHANGED
#define WM_DPICHANGED 0x02E0 // From Windows SDK 8.1+ headers
#endif

// Forward declare message handler from imgui_impl_win32.cpp
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);


LRESULT WINAPI DMARender::WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, msg, wParam, lParam))
        return true;

    switch (msg)
    {
    case WM_SIZE:
    {
        if (wParam == SIZE_MINIMIZED)
            return 0;
        RenderWindow* rndPtr = DMARender::hwndMap[hWnd];
        rndPtr->_setResizeParams((UINT)LOWORD(lParam), (UINT)HIWORD(lParam)); // Queue resize
        return 0;
    }
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        ::PostQuitMessage(0);
        return 0;
    case WM_DPICHANGED:
        if (ImGui::GetIO().ConfigFlags & ImGuiConfigFlags_DpiEnableScaleViewports)
        {
            //const int dpi = HIWORD(wParam);
            //printf("WM_DPICHANGED to %d (%.0f%%)\n", dpi, (float)dpi / 96.0f * 100.0f);
            const RECT* suggested_rect = (RECT*)lParam;
            ::SetWindowPos(hWnd, nullptr, suggested_rect->left, suggested_rect->top, suggested_rect->right - suggested_rect->left, suggested_rect->bottom - suggested_rect->top, SWP_NOZORDER | SWP_NOACTIVATE);
        }
        break;
    case WM_SETCURSOR:
        // Fix cursor spinning issue - force normal cursor in client area
        if (LOWORD(lParam) == HTCLIENT) {
            SetCursor(LoadCursor(NULL, IDC_ARROW));
            return TRUE;
        }
        break;
    }
    return ::DefWindowProcW(hWnd, msg, wParam, lParam);
}

void DMARender::RenderWindow::drawLootListTab()
{
    auto lootManager = bridge->getLootListManager();
    if (!lootManager) return;

    const auto& rareItemsWithColors = lootManager->getRareItemsWithColors();

    // Pagination settings
    static int currentPage = 0;
    const int itemsPerPage = 25;
    const int totalItems = static_cast<int>(rareItemsWithColors.size());
    const int totalPages = (totalItems + itemsPerPage - 1) / itemsPerPage; // Ceiling division

    ImGui::Text("Rare Items: %d (Page %d of %d)", totalItems, currentPage + 1, totalPages);
    ImGui::Separator();

    // Control buttons
    if (ImGui::Button("Clear All")) {
        lootManager->clearAll();
    }
    ImGui::SameLine();
    if (ImGui::Button("Save to File")) {
        if (lootManager->saveToFile()) {
            // Show success feedback briefly
            static auto lastSaveTime = std::chrono::steady_clock::now();
            lastSaveTime = std::chrono::steady_clock::now();
        }
    }

    // Show save feedback
    static auto lastSaveTime = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastSave = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastSaveTime).count();
    if (timeSinceLastSave < 2000) { // Show for 2 seconds
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0, 1, 0, 1), "Saved!");
    }
    ImGui::SameLine();
    if (ImGui::Button("Load from File")) {
        lootManager->loadFromFile();
    }

    ImGui::Separator();

    // Add new item functionality
    static char newItemBuffer[256] = "";
    ImGui::InputText("Add Item", newItemBuffer, sizeof(newItemBuffer));
    ImGui::SameLine();
    if (ImGui::Button("Add") && strlen(newItemBuffer) > 0) {
        std::string itemName = std::string(newItemBuffer);
        lootManager->addRareItem(itemName, IM_COL32(255, 0, 255, 255), itemName); // Add with default purple and original name as custom name
        lootManager->saveToFile(); // Auto-save when item added
        // Sync to Entity system
        // Note: This will be handled by the periodic sync in adapters
        memset(newItemBuffer, 0, sizeof(newItemBuffer)); // Clear buffer
    }

    ImGui::Separator();

    // Search functionality
    static char searchBuffer[256] = "";
    ImGui::InputText("Search", searchBuffer, sizeof(searchBuffer));

    ImGui::Separator();

    // Pagination controls
    if (totalPages > 1) {
        // Previous page button
        if (ImGui::Button("< Previous") && currentPage > 0) {
            currentPage--;
        }
        ImGui::SameLine();

        // Page info and direct page input
        ImGui::Text("Page:");
        ImGui::SameLine();
        ImGui::SetNextItemWidth(60);
        int displayPage = currentPage + 1; // Display 1-based page numbers
        if (ImGui::InputInt("##page", &displayPage, 0, 0)) {
            // Clamp to valid range
            if (displayPage < 1) displayPage = 1;
            if (displayPage > totalPages) displayPage = totalPages;
            currentPage = displayPage - 1; // Convert back to 0-based
        }
        ImGui::SameLine();
        ImGui::Text("of %d", totalPages);
        ImGui::SameLine();

        // Next page button
        if (ImGui::Button("Next >") && currentPage < totalPages - 1) {
            currentPage++;
        }

        ImGui::Separator();
    }

    // Create a filtered and sorted list first to get exact count
    std::vector<std::pair<std::string, DMARender::RareItemData>> filteredItems;
    std::string searchStr = searchBuffer;

    for (const auto& pair : rareItemsWithColors) {
        const std::string& itemName = pair.first;
        const auto& itemData = pair.second;

        // Filter by search
        if (!searchStr.empty()) {
            std::string lowerItemName = itemName;
            std::string lowerSearchStr = searchStr;
            std::transform(lowerItemName.begin(), lowerItemName.end(), lowerItemName.begin(), ::tolower);
            std::transform(lowerSearchStr.begin(), lowerSearchStr.end(), lowerSearchStr.begin(), ::tolower);
            if (lowerItemName.find(lowerSearchStr) == std::string::npos) {
                continue;
            }
        }

        filteredItems.push_back(pair);
    }

    // Sort filtered items alphabetically by display name
    std::sort(filteredItems.begin(), filteredItems.end(),
        [](const auto& a, const auto& b) {
            return a.second.customName < b.second.customName;
        });

    // Calculate pagination based on filtered results
    const int filteredTotalItems = static_cast<int>(filteredItems.size());
    const int filteredTotalPages = (filteredTotalItems + itemsPerPage - 1) / itemsPerPage;

    // Clamp current page to valid range for filtered results
    if (currentPage >= filteredTotalPages && filteredTotalPages > 0) {
        currentPage = filteredTotalPages - 1;
    }

    // Calculate start and end indices for current page
    const int startIndex = currentPage * itemsPerPage;
    const int endIndex = (startIndex + itemsPerPage < filteredTotalItems) ? startIndex + itemsPerPage : filteredTotalItems;

    // Calculate exact number of items that will be displayed
    const int actualItemsToShow = (endIndex > startIndex) ? endIndex - startIndex : 0;

    // Calculate very tight table height to eliminate all empty space
    const float rowHeight = ImGui::GetTextLineHeightWithSpacing() * 1.2f; // Further reduced for tighter fit
    const float headerHeight = ImGui::GetTextLineHeightWithSpacing(); // Just the basic line height
    const float tableHeight = headerHeight + (rowHeight * actualItemsToShow);

    if (ImGui::BeginTable("RareItemsTable", 4, ImGuiTableFlags_Borders | ImGuiTableFlags_RowBg, ImVec2(0, tableHeight))) {
        ImGui::TableSetupColumn("Display Name", ImGuiTableColumnFlags_WidthStretch);
        ImGui::TableSetupColumn("Original Name", ImGuiTableColumnFlags_WidthFixed, 150.0f);
        ImGui::TableSetupColumn("Color", ImGuiTableColumnFlags_WidthFixed, 80.0f);
        ImGui::TableSetupColumn("Actions", ImGuiTableColumnFlags_WidthFixed, 80.0f);
        ImGui::TableHeadersRow();

        // Use the already created filtered and sorted list from above

        // Display only items for current page
        for (int i = startIndex; i < endIndex; i++) {
            const auto& pair = filteredItems[i];
            const std::string& itemName = pair.first;
            const auto& itemData = pair.second;

            ImGui::TableNextRow();

            // Custom display name (editable)
            ImGui::TableSetColumnIndex(0);
            ImVec4 itemColor = ImGui::ColorConvertU32ToFloat4(itemData.color);

            static std::map<std::string, std::string> editBuffers;
            if (editBuffers.find(itemName) == editBuffers.end()) {
                editBuffers[itemName] = itemData.customName;
            }

            std::string nameInputId = "##name" + itemName;
            ImGui::PushStyleColor(ImGuiCol_Text, itemColor);
            if (ImGui::InputText(nameInputId.c_str(), &editBuffers[itemName])) {
                lootManager->setItemCustomName(itemName, editBuffers[itemName]);
                lootManager->saveToFile(); // Auto-save when name changes
            }
            ImGui::PopStyleColor();

            // Original TypeName (read-only)
            ImGui::TableSetColumnIndex(1);
            ImGui::TextDisabled("%s", itemName.c_str());

            // Color picker
            ImGui::TableSetColumnIndex(2);
            std::string colorId = "##color" + itemName;
            ImVec4 currentColor = itemColor;
            if (ImGui::ColorEdit3(colorId.c_str(), (float*)&currentColor, ImGuiColorEditFlags_NoInputs | ImGuiColorEditFlags_NoLabel)) {
                ImU32 newColor = ImGui::ColorConvertFloat4ToU32(currentColor);
                lootManager->setItemColor(itemName, newColor);
                lootManager->saveToFile(); // Auto-save when color changes
            }

            // Actions
            ImGui::TableSetColumnIndex(3);
            std::string removeId = "X##" + itemName;
            if (ImGui::Button(removeId.c_str())) {
                lootManager->removeRareItem(itemName);
                editBuffers.erase(itemName); // Clean up edit buffer
                lootManager->saveToFile(); // Auto-save when item removed
                // Note: Entity system sync will be handled by periodic sync in adapters
            }
        }

        ImGui::EndTable();
    }

    // Statistics
    ImGui::Separator();
    ImGui::Text("Statistics:");
    ImGui::Text("Total Rare Items: %zu", rareItemsWithColors.size());
    if (strlen(searchBuffer) > 0) {
        // Show filtered count when searching
        std::string searchStr = searchBuffer;
        int filteredCount = 0;
        for (const auto& pair : rareItemsWithColors) {
            std::string lowerItemName = pair.first;
            std::string lowerSearchStr = searchStr;
            std::transform(lowerItemName.begin(), lowerItemName.end(), lowerItemName.begin(), ::tolower);
            std::transform(lowerSearchStr.begin(), lowerSearchStr.end(), lowerSearchStr.begin(), ::tolower);
            if (lowerItemName.find(lowerSearchStr) != std::string::npos) {
                filteredCount++;
            }
        }
        ImGui::Text("Filtered Results: %d", filteredCount);
    }
    if (totalPages > 1) {
        int endItem = (currentPage + 1) * itemsPerPage;
        if (endItem > totalItems) endItem = totalItems;
        ImGui::Text("Showing items %d-%d",
            currentPage * itemsPerPage + 1,
            endItem);
    }
}

// Modern Red and Dark Grey Professional Theme
void DMARender::ApplyRedDarkGreyTheme()
{
    ImGuiStyle& style = ImGui::GetStyle();
    ImVec4* colors = style.Colors;

    // Red and Dark Grey Professional Color Palette
    // Primary: Bright red (#FF0000) on dark gray backgrounds
    // Surface: Professional dark grays and blacks
    // Text: White text for readability, red accents for highlights
    // Accent: Various red shades for depth and interaction

    // Text colors - white text on dark surfaces for readability
    colors[ImGuiCol_Text]                   = ImVec4(1.00f, 1.00f, 1.00f, 1.00f);  // White text
    colors[ImGuiCol_TextDisabled]           = ImVec4(0.60f, 0.60f, 0.60f, 1.00f);  // Light gray disabled text

    // Background colors - professional dark grays
    colors[ImGuiCol_WindowBg]               = ImVec4(0.08f, 0.08f, 0.08f, 1.00f);  // Very dark gray surface
    colors[ImGuiCol_ChildBg]                = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);  // Dark gray child backgrounds
    colors[ImGuiCol_PopupBg]                = ImVec4(0.10f, 0.10f, 0.10f, 0.98f);  // Dark gray popup

    // Border colors - bright red
    colors[ImGuiCol_Border]                 = ImVec4(0.80f, 0.00f, 0.00f, 0.70f);  // Bright red borders
    colors[ImGuiCol_BorderShadow]           = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);  // No shadow for clean look

    // Frame backgrounds - medium dark grays
    colors[ImGuiCol_FrameBg]                = ImVec4(0.15f, 0.15f, 0.15f, 1.00f);  // Medium dark gray frame
    colors[ImGuiCol_FrameBgHovered]         = ImVec4(0.60f, 0.00f, 0.00f, 0.40f);  // Red hover
    colors[ImGuiCol_FrameBgActive]          = ImVec4(0.80f, 0.00f, 0.00f, 0.60f);  // Bright red active

    // Title bar - dark with red accents
    colors[ImGuiCol_TitleBg]                = ImVec4(0.06f, 0.06f, 0.06f, 1.00f);  // Very dark title
    colors[ImGuiCol_TitleBgActive]          = ImVec4(0.40f, 0.00f, 0.00f, 1.00f);  // Dark red active
    colors[ImGuiCol_TitleBgCollapsed]       = ImVec4(0.10f, 0.10f, 0.10f, 1.00f);  // Dark gray collapsed
    
    // Menu bar - professional dark
    colors[ImGuiCol_MenuBarBg]              = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);  // Dark gray menu
    
    // Scrollbar - dark with red accents
    colors[ImGuiCol_ScrollbarBg]            = ImVec4(0.10f, 0.10f, 0.10f, 1.00f);  // Dark scrollbar bg
    colors[ImGuiCol_ScrollbarGrab]          = ImVec4(0.60f, 0.00f, 0.00f, 0.80f);  // Red grab
    colors[ImGuiCol_ScrollbarGrabHovered]   = ImVec4(0.80f, 0.00f, 0.00f, 0.90f);  // Bright red hover
    colors[ImGuiCol_ScrollbarGrabActive]    = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Full red active
    
    // Check mark and sliders - bright red
    colors[ImGuiCol_CheckMark]              = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Bright red check
    colors[ImGuiCol_SliderGrab]             = ImVec4(0.80f, 0.00f, 0.00f, 1.00f);  // Red slider
    colors[ImGuiCol_SliderGrabActive]       = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Full red active

    // Buttons - dark gray with red interactions
    colors[ImGuiCol_Button]                 = ImVec4(0.18f, 0.18f, 0.18f, 1.00f);  // Dark gray button
    colors[ImGuiCol_ButtonHovered]          = ImVec4(0.60f, 0.00f, 0.00f, 0.80f);  // Red hover
    colors[ImGuiCol_ButtonActive]           = ImVec4(0.80f, 0.00f, 0.00f, 1.00f);  // Bright red active
    
    // Headers - dark with red highlights
    colors[ImGuiCol_Header]                 = ImVec4(0.60f, 0.00f, 0.00f, 0.31f);  // Red header
    colors[ImGuiCol_HeaderHovered]          = ImVec4(0.80f, 0.00f, 0.00f, 0.50f);  // Bright red hover
    colors[ImGuiCol_HeaderActive]           = ImVec4(1.00f, 0.00f, 0.00f, 0.70f);  // Full red active

    // Separators - glowing red lines
    colors[ImGuiCol_Separator]              = ImVec4(0.60f, 0.00f, 0.00f, 0.60f);  // Red separator
    colors[ImGuiCol_SeparatorHovered]       = ImVec4(0.80f, 0.00f, 0.00f, 0.80f);  // Bright red hover
    colors[ImGuiCol_SeparatorActive]        = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Full red active
    
    // Resize grip - subtle red
    colors[ImGuiCol_ResizeGrip]             = ImVec4(0.60f, 0.00f, 0.00f, 0.25f);  // Red grip
    colors[ImGuiCol_ResizeGripHovered]      = ImVec4(0.80f, 0.00f, 0.00f, 0.70f);  // Bright red hover
    colors[ImGuiCol_ResizeGripActive]       = ImVec4(1.00f, 0.00f, 0.00f, 0.95f);  // Full red active

    // Tabs - dark gray with red selection
    colors[ImGuiCol_Tab]                    = ImVec4(0.15f, 0.15f, 0.15f, 1.00f);  // Dark gray inactive tab
    colors[ImGuiCol_TabHovered]             = ImVec4(0.60f, 0.00f, 0.00f, 0.50f);  // Red hover
    colors[ImGuiCol_TabActive]              = ImVec4(0.80f, 0.00f, 0.00f, 1.00f);  // Bright red active
    colors[ImGuiCol_TabUnfocused]           = ImVec4(0.12f, 0.12f, 0.12f, 1.00f);  // Dark gray unfocused
    colors[ImGuiCol_TabUnfocusedActive]     = ImVec4(0.40f, 0.00f, 0.00f, 1.00f);  // Dark red unfocused active

    // Docking - dark with red highlights
    colors[ImGuiCol_DockingPreview]         = ImVec4(0.80f, 0.00f, 0.00f, 0.70f);  // Red preview
    colors[ImGuiCol_DockingEmptyBg]         = ImVec4(0.08f, 0.08f, 0.08f, 1.00f);  // Dark background

    // Plots - bright red data visualization
    colors[ImGuiCol_PlotLines]              = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Bright red lines
    colors[ImGuiCol_PlotLinesHovered]       = ImVec4(1.00f, 0.40f, 0.40f, 1.00f);  // Light red hover
    colors[ImGuiCol_PlotHistogram]          = ImVec4(0.80f, 0.00f, 0.00f, 1.00f);  // Red histogram
    colors[ImGuiCol_PlotHistogramHovered]   = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Full red hover

    // Tables - dark with red accents
    colors[ImGuiCol_TableHeaderBg]          = ImVec4(0.15f, 0.15f, 0.15f, 1.00f);  // Dark gray header
    colors[ImGuiCol_TableBorderStrong]      = ImVec4(0.80f, 0.00f, 0.00f, 1.00f);  // Strong red border
    colors[ImGuiCol_TableBorderLight]       = ImVec4(0.60f, 0.00f, 0.00f, 0.60f);  // Light red border
    colors[ImGuiCol_TableRowBg]             = ImVec4(0.00f, 0.00f, 0.00f, 0.00f);  // Transparent
    colors[ImGuiCol_TableRowBgAlt]          = ImVec4(0.15f, 0.15f, 0.15f, 0.30f);  // Dark gray alternate rows

    // Selection and highlighting - red accents
    colors[ImGuiCol_TextSelectedBg]         = ImVec4(0.60f, 0.00f, 0.00f, 0.35f);  // Red selection
    colors[ImGuiCol_DragDropTarget]         = ImVec4(1.00f, 0.00f, 0.00f, 0.90f);  // Bright red target
    colors[ImGuiCol_NavHighlight]           = ImVec4(1.00f, 0.00f, 0.00f, 1.00f);  // Bright red navigation
    colors[ImGuiCol_NavWindowingHighlight]  = ImVec4(1.00f, 0.00f, 0.00f, 0.70f);  // Red windowing
    colors[ImGuiCol_NavWindowingDimBg]      = ImVec4(0.20f, 0.20f, 0.20f, 0.20f);  // Dark gray dim
    colors[ImGuiCol_ModalWindowDimBg]       = ImVec4(0.10f, 0.10f, 0.10f, 0.35f);  // Very dark gray modal dim

    // Style adjustments for modern red and dark grey theme
    style.WindowRounding    = 6.0f;    // Subtle rounded corners for modern feel
    style.FrameRounding     = 4.0f;    // Smooth frame corners
    style.PopupRounding     = 6.0f;    // Consistent popup rounding
    style.ScrollbarRounding = 4.0f;    // Smooth scrollbars
    style.GrabRounding      = 3.0f;    // Rounded grab handles
    style.TabRounding       = 4.0f;    // Modern tab design
    style.ChildRounding     = 4.0f;    // Subtle child element rounding
    
    // Professional spacing optimized for readability
    style.WindowPadding     = ImVec2(12, 12);  // Comfortable window padding
    style.FramePadding      = ImVec2(8, 6);    // Frame padding
    style.ItemSpacing       = ImVec2(8, 6);    // Item spacing
    style.ItemInnerSpacing  = ImVec2(6, 4);    // Inner element spacing
    style.IndentSpacing     = 20.0f;           // Clear indentation
    style.CellPadding       = ImVec2(6, 4);    // Table cell padding

    // Border styling with red glow effect
    style.WindowBorderSize  = 1.5f;           // Slightly thicker borders for red glow effect
    style.ChildBorderSize   = 1.0f;           // Child element borders
    style.FrameBorderSize   = 1.0f;           // Frame borders
    style.PopupBorderSize   = 1.0f;           // Popup borders
    style.TabBorderSize     = 0.0f;           // Clean tab design
    
    // Professional window title alignment
    style.WindowTitleAlign  = ImVec2(0.5f, 0.5f);  // Center window titles
}
