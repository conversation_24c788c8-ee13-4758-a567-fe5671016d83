#include "EnhancedFontManager.h"
#include "imgui.h"
#include <iostream>

namespace DMARender {

bool EnhancedFontManager::initialize() {
    // Load both system and embedded fonts (DayZ2 dual system)
    bool systemFontsLoaded = loadSystemFonts();
    bool embeddedFontsLoaded = loadEmbeddedFonts();
    
    if (!systemFontsLoaded && !embeddedFontsLoaded) {
        std::cerr << "Failed to load any fonts!" << std::endl;
        return false;
    }
    
    // Setup font mappings for different ESP elements
    setupFontMappings();
    
    return true;
}

bool EnhancedFontManager::loadSystemFonts() {
    ImGuiIO& io = ImGui::GetIO();
    
    // Create DayZ2-style high-quality font configuration
    ImFontConfig config;
    config.PixelSnapH = fontConfig.PixelSnapH;
    config.FontDataOwnedByAtlas = fontConfig.FontDataOwnedByAtlas;
    config.OversampleH = fontConfig.OversampleH;
    config.OversampleV = fontConfig.OversampleV;
    config.RasterizerMultiply = fontConfig.RasterizerMultiply;
    config.GlyphRanges = (const ImWchar*)fontConfig.GlyphRanges;
    
    // Load Tahoma font (DayZ2 system font)
    tahomaFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\tahoma.ttf", 
                                             SMALL_FONT_SIZE, &config, 
                                             (const ImWchar*)fontConfig.GlyphRanges);
    
    if (!tahomaFont) {
        // Fallback to Consolas if Tahoma fails
        tahomaFont = io.Fonts->AddFontFromFileTTF("C:\\Windows\\Fonts\\consola.ttf", 
                                                 SMALL_FONT_SIZE, &config,
                                                 (const ImWchar*)fontConfig.GlyphRanges);
    }
    
    return tahomaFont != nullptr;
}

bool EnhancedFontManager::loadEmbeddedFonts() {
    ImGuiIO& io = ImGui::GetIO();
    
    // Load embedded Cousine font (DayZ2 primary font)
    cousineFont = io.Fonts->AddFontFromMemoryCompressedTTF(
        EmbeddedFonts::Cousine_compressed_data,
        EmbeddedFonts::Cousine_compressed_size,
        MEDIUM_FONT_SIZE
    );
    
    return cousineFont != nullptr;
}

void EnhancedFontManager::setupFontMappings() {
    // Map font types to actual fonts (prefer Cousine, fallback to Tahoma)
    ImFont* primaryFont = cousineFont ? cousineFont : tahomaFont;
    ImFont* secondaryFont = tahomaFont ? tahomaFont : cousineFont;
    
    if (primaryFont) {
        fonts[FontType::PLAYER_NAME] = primaryFont;
        fonts[FontType::LOOT_NEAR] = primaryFont;
        fonts[FontType::ZOMBIE_TEXT] = primaryFont;
        fonts[FontType::ANIMAL_TEXT] = primaryFont;
        fonts[FontType::UI_TEXT] = primaryFont;
    }
    
    if (secondaryFont) {
        fonts[FontType::PLAYER_DISTANCE] = secondaryFont;
        fonts[FontType::PLAYER_HAND] = secondaryFont;
        fonts[FontType::LOOT_FAR] = secondaryFont;
        fonts[FontType::DEBUG_TEXT] = secondaryFont;
    }
}

ImFont* EnhancedFontManager::getFont(FontType type) const {
    auto it = fonts.find(type);
    if (it != fonts.end() && it->second) {
        return it->second;
    }
    
    // Fallback to any available font
    if (cousineFont) return cousineFont;
    if (tahomaFont) return tahomaFont;
    return ImGui::GetFont(); // Ultimate fallback
}

void EnhancedFontManager::drawText(ImDrawList* drawList, const std::string& text, 
                                  ImVec2 position, ImU32 color, FontType fontType,
                                  const StrokeConfig& stroke) {
    ImFont* font = getFont(fontType);
    float fontSize = font->FontSize;
    
    // Draw stroke/outline if enabled (DayZ2 style)
    if (stroke.enabled) {
        if (stroke.strokeDirections == 8) {
            drawTextStroke8Direction(drawList, font, fontSize, position, text, stroke);
        } else {
            drawTextStroke4Direction(drawList, font, fontSize, position, text, stroke);
        }
    }
    
    // Draw main text
    drawList->AddText(font, fontSize, position, color, text.c_str());
}

void EnhancedFontManager::drawTextWithSize(ImDrawList* drawList, const std::string& text,
                                          ImVec2 position, float fontSize, ImU32 color,
                                          const StrokeConfig& stroke) {
    ImFont* font = cousineFont ? cousineFont : (tahomaFont ? tahomaFont : ImGui::GetFont());
    
    // Draw stroke/outline if enabled
    if (stroke.enabled) {
        if (stroke.strokeDirections == 8) {
            drawTextStroke8Direction(drawList, font, fontSize, position, text, stroke);
        } else {
            drawTextStroke4Direction(drawList, font, fontSize, position, text, stroke);
        }
    }
    
    // Draw main text
    drawList->AddText(font, fontSize, position, color, text.c_str());
}

void EnhancedFontManager::drawCenteredText(ImDrawList* drawList, const std::string& text,
                                          ImVec2 position, ImU32 color, FontType fontType,
                                          const StrokeConfig& stroke) {
    ImVec2 textSize = calculateTextSize(text, fontType);
    ImVec2 centeredPos = ImVec2(position.x - textSize.x * 0.5f, position.y - textSize.y * 0.5f);
    drawText(drawList, text, centeredPos, color, fontType, stroke);
}

ImVec2 EnhancedFontManager::calculateTextSize(const std::string& text, FontType fontType) const {
    ImFont* font = getFont(fontType);
    return font->CalcTextSizeA(font->FontSize, FLT_MAX, 0.0f, text.c_str());
}

ImVec2 EnhancedFontManager::calculateTextSizeWithFont(const std::string& text, float fontSize) const {
    ImFont* font = cousineFont ? cousineFont : (tahomaFont ? tahomaFont : ImGui::GetFont());
    return font->CalcTextSizeA(fontSize, FLT_MAX, 0.0f, text.c_str());
}

void EnhancedFontManager::drawTextStroke4Direction(ImDrawList* drawList, ImFont* font, 
                                                  float fontSize, ImVec2 position,
                                                  const std::string& text, 
                                                  const StrokeConfig& stroke) {
    // DayZ2-style 4-direction stroke (diagonal corners)
    float offset = stroke.strokeWidth;
    
    drawList->AddText(font, fontSize, ImVec2(position.x + offset, position.y + offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x - offset, position.y - offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x + offset, position.y - offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x - offset, position.y + offset), stroke.strokeColor, text.c_str());
}

void EnhancedFontManager::drawTextStroke8Direction(ImDrawList* drawList, ImFont* font,
                                                  float fontSize, ImVec2 position,
                                                  const std::string& text,
                                                  const StrokeConfig& stroke) {
    // Enhanced 8-direction stroke for even better visibility
    float offset = stroke.strokeWidth;
    
    // 4 cardinal directions
    drawList->AddText(font, fontSize, ImVec2(position.x + offset, position.y), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x - offset, position.y), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x, position.y + offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x, position.y - offset), stroke.strokeColor, text.c_str());
    
    // 4 diagonal directions
    drawList->AddText(font, fontSize, ImVec2(position.x + offset, position.y + offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x - offset, position.y - offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x + offset, position.y - offset), stroke.strokeColor, text.c_str());
    drawList->AddText(font, fontSize, ImVec2(position.x - offset, position.y + offset), stroke.strokeColor, text.c_str());
}

void EnhancedFontManager::reloadFonts() {
    // Clear existing fonts
    fonts.clear();
    tahomaFont = nullptr;
    cousineFont = nullptr;
    
    // Rebuild font atlas
    ImGui::GetIO().Fonts->Clear();
    
    // Reinitialize
    initialize();
}

} // namespace DMARender
