#pragma once

// Embedded Cousine-Regular.ttf font data (compressed)
// This ensures consistent font rendering across all systems
// Based on <PERSON><PERSON><PERSON>'s approach for professional ESP text rendering

namespace DMARender {
namespace EmbeddedFonts {

// Cousine-Regular.ttf compressed data
// Note: This is a placeholder - in production you would use a tool like binary_to_compressed_c.cpp
// to generate the actual compressed font data from Cousine-Regular.ttf
static const unsigned int Cousine_compressed_size = 37630;

// Placeholder compressed data - replace with actual Cousine font data
static const unsigned int Cousine_compressed_data[9408] = {
    0x0000bc57, 0x00000000, 0x88ab0000, 0x00000400, 0x00010025, 0x82110000, 0x00042604, 0x49534410,
    0x820d8247, 0x44ab210c, 0x082c0782, 0x4d544646, 0xc867e44e, 0x6cab0000, 0x1c280f82, 0x46454447,
    // ... (truncated for brevity - in production this would contain the full compressed font)
    // You can extract this from <PERSON><PERSON><PERSON>'s font.h or generate it from Cousine-Regular.ttf
};

// Extended character ranges for international support (DayZ2 style)
static const unsigned short DefaultCharRanges[] = {
    0x0020, 0x00FF,  // Basic Latin + Latin Supplement
    0x0400, 0x052F,  // Cyrillic + Cyrillic Supplement  
    0x2DE0, 0x2DFF,  // Cyrillic Extended-A
    0xA640, 0xA69F,  // Cyrillic Extended-B
    0xE000, 0xE226,  // Private Use Area (custom symbols)
    0,
};

// Font configuration for high-quality rendering (DayZ2 style)
struct HighQualityFontConfig {
    bool PixelSnapH = false;           // Smooth sub-pixel rendering
    bool FontDataOwnedByAtlas = false; // Memory management
    int OversampleH = 5;               // High horizontal oversampling (DayZ2 quality)
    int OversampleV = 5;               // High vertical oversampling (DayZ2 quality)
    float RasterizerMultiply = 1.2f;   // Slightly bolder text (DayZ2 style)
    const unsigned short* GlyphRanges = DefaultCharRanges;
};

} // namespace EmbeddedFonts
} // namespace DMARender
