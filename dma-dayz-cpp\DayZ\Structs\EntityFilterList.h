#pragma once

#include <vector>
#include <string>
#include <unordered_set>

//CASESENSITIVE!!!!!!!!
namespace DayZ {
	const std::vector<std::string> InventoryItemWhitelist{ 
		"inventoryItem", "itemoptics", "carwheel", "ItemBook", "ItemSuppressor"	w			
	};
	const std::vector<std::string> itemBlacklist {
		"AreaDamageTriggerBase", "TrapTrigger", "TripWireTrigger", "ClutterCutter6x6", 
		"ContaminatedTrigger_Dynamic", 
		"ContaminatedArea_Dynamic", "ContaminatedArea_Static", "ContaminatedTrigger", 
		"AreaDamageTriggerBase", "TreeEffecter", "FreeDebugCamera", 
		"SnapPoint", "TerritoryTrigger", "AP_FarmTrigger","AP_TamedTrigger",
		"NoBuildZone", "SafeZoneTrigger", "Beam",
		"Fog_30_MovingWind1", "$UNT$Clutter Cutter 3x3", "ClutterCutter6x6", "SafeZoneTeleportTrigger",
		"house", "ContaminatedArea_Static", "ContaminatedTrigger", "TerritoryTrigger", "TrapTrigger", "SafezoneTrigger", "GunRangeTrigger", "SafeZoneExitTeleportTrigger", "UndergroundTriggerCarrier", "UndergroundTrigger"
	};

	// Performance optimization: Hash set for O(1) blacklist lookups
	const std::unordered_set<std::string> itemBlacklistSet(itemBlacklist.begin(), itemBlacklist.end());
}