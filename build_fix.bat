@echo off
echo ========================================
echo Building DayZ DMA Project
echo ========================================
echo.

echo Checking Visual Studio installation...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    echo Found Visual Studio 2022 Community
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    echo Found Visual Studio 2022 Professional
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD="C:\Program Files\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    echo Found Visual Studio 2022 Enterprise
) else (
    echo ERROR: Visual Studio 2022 not found!
    echo Please install Visual Studio 2022 with C++ development tools
    pause
    exit /b 1
)

echo.
echo Cleaning previous build...
if exist "x64\Release" rmdir /s /q "x64\Release"
if exist "x64\Debug" rmdir /s /q "x64\Debug"

echo.
echo Building DMALib first...
%MSBUILD% DMALib\DMALib.vcxproj /p:Configuration=Release /p:Platform=x64 /verbosity:minimal
if errorlevel 1 (
    echo ERROR: Failed to build DMALib!
    pause
    exit /b 1
)

echo.
echo Building main project...
%MSBUILD% dma-dayz-cpp.sln /p:Configuration=Release /p:Platform=x64 /verbosity:normal
if errorlevel 1 (
    echo.
    echo ========================================
    echo BUILD FAILED - Compilation Errors Found
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Common fixes:
    echo 1. Check for missing semicolons
    echo 2. Check for missing includes
    echo 3. Check for syntax errors
    echo 4. Check for missing dependencies
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Output files should be in: x64\Release\
    echo.
)

pause 