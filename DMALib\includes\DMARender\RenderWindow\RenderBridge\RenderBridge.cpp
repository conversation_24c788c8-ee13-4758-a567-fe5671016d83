#include "RenderBridge.h"
#include "../../Settings/SettingsManager.h"
#include <windows.h>
#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <cctype>
#include <cstdlib>
#include <fstream>
#include <set>
#include <cmath>
#include "imgui.h"

// Forward declarations for DayZ structures
namespace DayZ {
    class OverlayAdapter;
    class MemoryUpdater;
    class Camera;
}

DMARender::RenderBridge::RenderBridge(ID3D11Device** g_pd3dDevice)
{
	fflush(stdout);

	this->g_pd3dDevicePtr = g_pd3dDevice;
	this->mapManager = std::shared_ptr<MapManager>(new MapManager(g_pd3dDevice));
	this->lootListManager = std::shared_ptr<LootListManager>(new LootListManager());
	this->settingsManager = std::shared_ptr<SettingsManager>(new SettingsManager());

	fflush(stdout);

	// Load settings on startup
	loadSettings();

	// Load admin list on startup
	loadAdminSteamIDs("steamids.txt");



	fflush(stdout);
}

void DMARender::RenderBridge::createFonts()
{
	if (this->getOverlay()) {
		this->getOverlay()->createFonts();
	}
	if (this->getRadar()) {
		this->getRadar()->createFonts();
	}
}

void DMARender::RenderBridge::setOverlay(std::shared_ptr<IOverlay> ptr)
{
	this->overlayPtr = ptr;
}

std::shared_ptr<DMARender::IOverlay> DMARender::RenderBridge::getOverlay()
{
	return this->overlayPtr;
}

void DMARender::RenderBridge::setRadar(std::shared_ptr<IRadar> ptr)
{
	this->radarPtr = ptr;
}

std::shared_ptr<DMARender::IRadar> DMARender::RenderBridge::getRadar()
{
	return this->radarPtr;
}

void DMARender::RenderBridge::addMap(std::shared_ptr<IGameMap> map)
{
	this->mapManager->addMap(map);
}

std::shared_ptr<DMARender::MapManager> DMARender::RenderBridge::getMapManager()
{
	return mapManager;
}

std::shared_ptr<DMARender::LootListManager> DMARender::RenderBridge::getLootListManager()
{
	return lootListManager;
}

std::shared_ptr<DMARender::SettingsManager> DMARender::RenderBridge::getSettingsManager()
{
	return settingsManager;
}

void DMARender::RenderBridge::saveSettings()
{
	if (settingsManager) {
		fflush(stdout);
		// Load current settings from bridge to settings manager
		settingsManager->loadFromRenderBridge(this);

		// Save to file
		settingsManager->saveSettings();
	} else {
		fflush(stdout);
	}
}

void DMARender::RenderBridge::loadSettings()
{
	fflush(stdout);
	if (settingsManager) {
		// Load settings from file
		settingsManager->loadSettings();

		// Apply settings to bridge
		settingsManager->applyToRenderBridge(this);

		fflush(stdout);
	} else {
		fflush(stdout);
	}
}

bool DMARender::RenderBridge::isPlayerExcluded(const std::string& steamID) const
{
	if (!settingsManager) return false;

	const auto& excludedList = settingsManager->getSettings().excludedPlayerSteamIDs;
	return std::find(excludedList.begin(), excludedList.end(), steamID) != excludedList.end();
}

void DMARender::RenderBridge::addExcludedPlayer(const std::string& steamID)
{
	if (!settingsManager) return;

	auto& excludedList = settingsManager->getSettings().excludedPlayerSteamIDs;
	if (std::find(excludedList.begin(), excludedList.end(), steamID) == excludedList.end()) {
		excludedList.push_back(steamID);
		fflush(stdout);
		saveSettings(); // Auto-save when adding excluded player
	} else {
		fflush(stdout);
	}
}

void DMARender::RenderBridge::removeExcludedPlayer(const std::string& steamID)
{
	if (!settingsManager) return;

	auto& excludedList = settingsManager->getSettings().excludedPlayerSteamIDs;
	auto it = std::find(excludedList.begin(), excludedList.end(), steamID);
	if (it != excludedList.end()) {
		excludedList.erase(it);
		fflush(stdout);
		saveSettings(); // Auto-save when removing excluded player
	} else {
		fflush(stdout);
	}
}

const std::vector<std::string>& DMARender::RenderBridge::getExcludedPlayers() const
{
	static const std::vector<std::string> empty;
	if (!settingsManager) return empty;

	return settingsManager->getSettings().excludedPlayerSteamIDs;
}

void DMARender::RenderBridge::loadAdminSteamIDs(const std::string& filePath)
{
	std::ifstream file(filePath);
	if (!file.is_open()) {
		fflush(stdout);
		return;
	}

	std::string line;
	while (std::getline(file, line)) {
		if (!line.empty()) {
			adminSteamIDs.insert(line);
		}
	}

	file.close();
	fflush(stdout);
}

void DMARender::RenderBridge::saveAdminSteamIDs(const std::string& filePath)
{
	std::ofstream file(filePath);
	if (!file.is_open()) {
		fflush(stdout);
		return;
	}

	for (const auto& steamID : adminSteamIDs) {
		file << steamID << std::endl;
	}

	file.close();
	fflush(stdout);
}

bool DMARender::RenderBridge::isPlayerAdmin(const std::string& steamID) const
{
	return adminSteamIDs.count(steamID) > 0;
}

void DMARender::RenderBridge::addAdminPlayer(const std::string& steamID)
{
	if (adminSteamIDs.insert(steamID).second) {
		saveAdminSteamIDs("steamids.txt");
		fflush(stdout);
	} else {
		fflush(stdout);
	}
}

void DMARender::RenderBridge::removeAdminPlayer(const std::string& steamID)
{
	if (adminSteamIDs.erase(steamID) > 0) {
		saveAdminSteamIDs("steamids.txt");
		fflush(stdout);
	} else {
		fflush(stdout);
	}
}

// Dynamic Font Detection Implementation
const std::vector<std::string>& DMARender::RenderBridge::getAvailableFontNames() const
{
	if (!fontListCached) {
		refreshFontList();
	}
	return availableFontNames;
}

const std::vector<std::string>& DMARender::RenderBridge::getAvailableFontFiles() const
{
	if (!fontListCached) {
		refreshFontList();
	}
	return availableFontFiles;
}

std::string DMARender::RenderBridge::getSelectedFontName() const
{
	const auto& names = getAvailableFontNames();
	if (selectedFontIndex >= 0 && selectedFontIndex < (int)names.size()) {
		return names[selectedFontIndex];
	}
	return "Unknown Font";
}

std::string DMARender::RenderBridge::getSelectedFontFile() const
{
	const auto& files = getAvailableFontFiles();
	if (selectedFontIndex >= 0 && selectedFontIndex < (int)files.size()) {
		return files[selectedFontIndex];
	}
	return "unknown.ttf";
}

std::string DMARender::RenderBridge::getSelectedFontFullPath() const
{
	const auto& files = getAvailableFontFiles();
	if (selectedFontIndex >= 0 && selectedFontIndex < (int)files.size()) {
		std::string selectedFile = files[selectedFontIndex];
		
		// Search for the font file in all font directories
		std::string username = "Public"; // Default fallback
		char* usernameBuffer = nullptr;
		size_t usernameSize = 0;
		if (_dupenv_s(&usernameBuffer, &usernameSize, "USERNAME") == 0 && usernameBuffer != nullptr) {
			username = std::string(usernameBuffer);
			free(usernameBuffer);
		}
		
		std::vector<std::string> fontDirectories = {
			"C:\\Windows\\Fonts\\",
			"C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\Fonts\\",
			"C:\\Program Files\\Common Files\\Fonts\\",
			"C:\\Program Files (x86)\\Common Files\\Fonts\\"
		};
		
		// Try each directory to find the font file and validate it
		for (const auto& directory : fontDirectories) {
			std::string fullPath = directory + selectedFile;
			
			// Check if file exists and validate it's safe to use
			WIN32_FIND_DATAA findData;
			HANDLE hFind = FindFirstFileA(fullPath.c_str(), &findData);
			if (hFind != INVALID_HANDLE_VALUE) {
				FindClose(hFind);
				
				// Additional safety checks for font file
				if (isValidFontFile(fullPath)) {
					return fullPath;
				} else {
					std::cout << "[FONT SAFETY] Font file validation failed: " << fullPath << std::endl;
				}
			}
		}
		
		// If not found in any directory or validation failed, return safe fallback
		std::cout << "[FONT SAFETY] Using safe fallback font instead of: " << selectedFile << std::endl;
		return "C:\\Windows\\Fonts\\arial.ttf";
	}
	return "C:\\Windows\\Fonts\\arial.ttf";
}

bool DMARender::RenderBridge::isValidFontFile(const std::string& fontPath) const
{
	try {
		// Check if file exists and is readable
		HANDLE hFile = CreateFileA(
			fontPath.c_str(),
			GENERIC_READ,
			FILE_SHARE_READ,
			NULL,
			OPEN_EXISTING,
			FILE_ATTRIBUTE_NORMAL,
			NULL
		);
		
		if (hFile == INVALID_HANDLE_VALUE) {
			return false;
		}
		
		// Check file size - reject empty files or extremely large files
		LARGE_INTEGER fileSize;
		if (!GetFileSizeEx(hFile, &fileSize)) {
			CloseHandle(hFile);
			return false;
		}
		
		// Reject files that are too small (< 1KB) or too large (> 50MB)
		if (fileSize.QuadPart < 1024 || fileSize.QuadPart > 52428800) {
			CloseHandle(hFile);
			std::cout << "[FONT SAFETY] Font file size invalid: " << fileSize.QuadPart << " bytes" << std::endl;
			return false;
		}
		
		// Read first few bytes to check font file signature
		char buffer[4] = {0};
		DWORD bytesRead = 0;
		if (!ReadFile(hFile, buffer, 4, &bytesRead, NULL) || bytesRead != 4) {
			CloseHandle(hFile);
			return false;
		}
		
		CloseHandle(hFile);
		
		// Check for valid font file signatures
		// TTF: starts with 0x00, 0x01, 0x00, 0x00 or "true" or "typ1"
		// OTF: starts with "OTTO"
		if ((buffer[0] == 0x00 && buffer[1] == 0x01 && buffer[2] == 0x00 && buffer[3] == 0x00) ||
			(memcmp(buffer, "true", 4) == 0) ||
			(memcmp(buffer, "typ1", 4) == 0) ||
			(memcmp(buffer, "OTTO", 4) == 0)) {
			return true;
		}
		
		std::cout << "[FONT SAFETY] Invalid font file signature in: " << fontPath << std::endl;
		return false;
	}
	catch (...) {
		std::cout << "[FONT SAFETY] Exception validating font: " << fontPath << std::endl;
		return false;
	}
}

int DMARender::RenderBridge::findFontIndexByName(const std::string& fontName) const
{
	const auto& names = getAvailableFontNames();
	for (int i = 0; i < (int)names.size(); i++) {
		if (names[i] == fontName) {
			return i;
		}
	}
	return -1; // Not found
}

void DMARender::RenderBridge::clearFontCache() const
{
	fontListCached = false;
	availableFontNames.clear();
	availableFontFiles.clear();
	std::cout << "[FONT DETECTION] Font cache cleared. Next access will trigger rescan." << std::endl;
}

void DMARender::RenderBridge::refreshFontList() const
{
	availableFontNames.clear();
	availableFontFiles.clear();
	
	std::cout << "[FONT DETECTION] Starting comprehensive font scan..." << std::endl;
	
	// Get username safely
	std::string username = "Public"; // Default fallback
	char* usernameBuffer = nullptr;
	size_t usernameSize = 0;
	if (_dupenv_s(&usernameBuffer, &usernameSize, "USERNAME") == 0 && usernameBuffer != nullptr) {
		username = std::string(usernameBuffer);
		free(usernameBuffer);
	}
	
	// List of font directories to scan
	std::vector<std::string> fontDirectories = {
		"C:\\Windows\\Fonts\\",
		"C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Windows\\Fonts\\",
		"C:\\Program Files\\Common Files\\Fonts\\",
		"C:\\Program Files (x86)\\Common Files\\Fonts\\"
	};
	
	// Scan each font directory
	for (const auto& fontsPath : fontDirectories) {
		std::cout << "[FONT DETECTION] Scanning directory: " << fontsPath << std::endl;
		
		std::string searchPattern = fontsPath + "*.*";
		
		WIN32_FIND_DATAA findData;
		HANDLE hFind = FindFirstFileA(searchPattern.c_str(), &findData);
		
		if (hFind != INVALID_HANDLE_VALUE) {
		do {
			// Skip directories
			if (findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) {
				continue;
			}
			
			std::string fileName = findData.cFileName;
			std::string lowerFileName = fileName;
			std::transform(lowerFileName.begin(), lowerFileName.end(), lowerFileName.begin(), ::tolower);
			
			// Check if it's a font file (common font extensions)
			auto hasExtension = [&lowerFileName](const std::string& ext) {
				return lowerFileName.length() >= ext.length() && 
				       lowerFileName.compare(lowerFileName.length() - ext.length(), ext.length(), ext) == 0;
			};
			
			if (hasExtension(".ttf") || 
				hasExtension(".otf") || 
				hasExtension(".ttc") ||
				hasExtension(".otc") ||
				hasExtension(".woff") ||
				hasExtension(".woff2")) {
				
				// Create display name from filename (remove extension and clean up)
				std::string displayName = fileName.substr(0, fileName.find_last_of('.'));
				
				// Try to make display name more readable
				// Replace underscores and hyphens with spaces
				std::replace(displayName.begin(), displayName.end(), '_', ' ');
				std::replace(displayName.begin(), displayName.end(), '-', ' ');
				
				// Capitalize first letter of each word
				bool capitalizeNext = true;
				for (char& c : displayName) {
					if (c == ' ') {
						capitalizeNext = true;
					} else if (capitalizeNext) {
						c = std::toupper(c);
						capitalizeNext = false;
					}
				}
				
				// Check for duplicates (same filename from different directories)
				bool isDuplicate = false;
				for (const auto& existingFile : availableFontFiles) {
					if (existingFile == fileName) {
						isDuplicate = true;
						break;
					}
				}
				
				// Add to our font list if not duplicate
				if (!isDuplicate) {
					availableFontFiles.push_back(fileName);
					availableFontNames.push_back(displayName);
				}
				
			}
		} while (FindNextFileA(hFind, &findData));
		FindClose(hFind);
		}
	}
	
	// Sort fonts alphabetically by display name for better UI
	std::vector<std::pair<std::string, std::string>> fontPairs;
	for (size_t i = 0; i < availableFontNames.size(); i++) {
		fontPairs.push_back({availableFontNames[i], availableFontFiles[i]});
	}
	
	std::sort(fontPairs.begin(), fontPairs.end(), [](const auto& a, const auto& b) {
		return a.first < b.first;
	});
	
	// Clear and rebuild sorted lists
	availableFontNames.clear();
	availableFontFiles.clear();
	
	for (const auto& pair : fontPairs) {
		availableFontNames.push_back(pair.first);
		availableFontFiles.push_back(pair.second);
	}
	
	// If no fonts found (very unlikely), add basic fallbacks
	if (availableFontNames.empty()) {
		std::cout << "[FONT DETECTION] No fonts found! Adding fallbacks..." << std::endl;
		availableFontNames.push_back("System Default");
		availableFontFiles.push_back("arial.ttf");
	}
	
	fontListCached = true;
	
	// Debug output
	std::cout << "[FONT DETECTION] Found " << availableFontNames.size() << " font files:" << std::endl;
	for (size_t i = 0; i < availableFontNames.size() && i < 15; i++) { // Show first 15
		std::cout << "[FONT DETECTION] " << i << ": " << availableFontNames[i] << " (" << availableFontFiles[i] << ")" << std::endl;
	}
	if (availableFontNames.size() > 15) {
		std::cout << "[FONT DETECTION] ... and " << (availableFontNames.size() - 15) << " more fonts" << std::endl;
	}
}

// ===== ESP COLOR GETTER FUNCTIONS =====

// Player ESP Colors
ImU32 DMARender::RenderBridge::getPlayerBoxColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.playerBoxColorR, settings.playerBoxColorG, settings.playerBoxColorB, settings.playerBoxColorA);
	}

	ImU32 DMARender::RenderBridge::getPlayerTextColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.playerTextColorR, settings.playerTextColorG, settings.playerTextColorB, settings.playerTextColorA);
	}

	ImU32 DMARender::RenderBridge::getPlayerNameColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.playerNameColorR, settings.playerNameColorG, settings.playerNameColorB, settings.playerNameColorA);
	}

	ImU32 DMARender::RenderBridge::getPlayerDistanceColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.playerDistanceColorR, settings.playerDistanceColorG, settings.playerDistanceColorB, settings.playerDistanceColorA);
	}

	ImU32 DMARender::RenderBridge::getPlayerHandColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.playerHandColorR, settings.playerHandColorG, settings.playerHandColorB, settings.playerHandColorA);
	}

	ImU32 DMARender::RenderBridge::getPlayerSkeletonColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.playerSkeletonColorR, settings.playerSkeletonColorG, settings.playerSkeletonColorB, settings.playerSkeletonColorA);
	}

	// Zombie ESP Colors
	ImU32 DMARender::RenderBridge::getZombieBoxColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.zombieBoxColorR, settings.zombieBoxColorG, settings.zombieBoxColorB, settings.zombieBoxColorA);
	}

	ImU32 DMARender::RenderBridge::getZombieTextColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.zombieTextColorR, settings.zombieTextColorG, settings.zombieTextColorB, settings.zombieTextColorA);
	}

	ImU32 DMARender::RenderBridge::getZombieSkeletonColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.zombieSkeletonColorR, settings.zombieSkeletonColorG, settings.zombieSkeletonColorB, settings.zombieSkeletonColorA);
	}

	// Animal ESP Colors
	ImU32 DMARender::RenderBridge::getAnimalBoxColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.animalBoxColorR, settings.animalBoxColorG, settings.animalBoxColorB, settings.animalBoxColorA);
	}

	ImU32 DMARender::RenderBridge::getAnimalTextColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.animalTextColorR, settings.animalTextColorG, settings.animalTextColorB, settings.animalTextColorA);
	}

	// Dead Player ESP Colors
	ImU32 DMARender::RenderBridge::getDeadPlayerBoxColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.deadPlayerBoxColorR, settings.deadPlayerBoxColorG, settings.deadPlayerBoxColorB, settings.deadPlayerBoxColorA);
	}

	ImU32 DMARender::RenderBridge::getDeadPlayerTextColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.deadPlayerTextColorR, settings.deadPlayerTextColorG, settings.deadPlayerTextColorB, settings.deadPlayerTextColorA);
	}

	// Vehicle ESP Colors
	ImU32 DMARender::RenderBridge::getVehicleBoxColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.vehicleBoxColorR, settings.vehicleBoxColorG, settings.vehicleBoxColorB, settings.vehicleBoxColorA);
	}

	ImU32 DMARender::RenderBridge::getVehicleTextColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.vehicleTextColorR, settings.vehicleTextColorG, settings.vehicleTextColorB, settings.vehicleTextColorA);
	}

	// Health Bar Colors
	ImU32 DMARender::RenderBridge::getHealthBarHighColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.healthBarHighColorR, settings.healthBarHighColorG, settings.healthBarHighColorB, settings.healthBarHighColorA);
	}

	ImU32 DMARender::RenderBridge::getHealthBarMediumColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.healthBarMediumColorR, settings.healthBarMediumColorG, settings.healthBarMediumColorB, settings.healthBarMediumColorA);
	}

	ImU32 DMARender::RenderBridge::getHealthBarLowColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.healthBarLowColorR, settings.healthBarLowColorG, settings.healthBarLowColorB, settings.healthBarLowColorA);
	}

	ImU32 DMARender::RenderBridge::getHealthBarBackgroundColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.healthBarBackgroundColorR, settings.healthBarBackgroundColorG, settings.healthBarBackgroundColorB, settings.healthBarBackgroundColorA);
	}

	// Container Contents Colors
	ImU32 DMARender::RenderBridge::getContainerContentsColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.containerContentsColorR, settings.containerContentsColorG, settings.containerContentsColorB, settings.containerContentsColorA);
	}

	// ===== LOOT CATEGORY ESP COLORS =====

	// Weapon ESP Colors
	ImU32 DMARender::RenderBridge::getWeaponColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.weaponColorR, settings.weaponColorG, settings.weaponColorB, settings.weaponColorA);
	}

	// Clothing ESP Colors
	ImU32 DMARender::RenderBridge::getClothingColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.clothingColorR, settings.clothingColorG, settings.clothingColorB, settings.clothingColorA);
	}

	// Backpack ESP Colors
	ImU32 DMARender::RenderBridge::getBackpackColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.backpackColorR, settings.backpackColorG, settings.backpackColorB, settings.backpackColorA);
	}

	// Food ESP Colors
	ImU32 DMARender::RenderBridge::getFoodColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.foodColorR, settings.foodColorG, settings.foodColorB, settings.foodColorA);
	}

	// Ammo ESP Colors
	ImU32 DMARender::RenderBridge::getAmmoColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.ammoColorR, settings.ammoColorG, settings.ammoColorB, settings.ammoColorA);
	}

	// Proxy Magazine ESP Colors
	ImU32 DMARender::RenderBridge::getProxyMagazineColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.proxyMagazineColorR, settings.proxyMagazineColorG, settings.proxyMagazineColorB, settings.proxyMagazineColorA);
	}

	// Optic ESP Colors
	ImU32 DMARender::RenderBridge::getOpticColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.opticColorR, settings.opticColorG, settings.opticColorB, settings.opticColorA);
	}

	// Base Building ESP Colors
	ImU32 DMARender::RenderBridge::getBaseBuildingColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.baseBuildingColorR, settings.baseBuildingColorG, settings.baseBuildingColorB, settings.baseBuildingColorA);
	}

	// Melee ESP Colors
	ImU32 DMARender::RenderBridge::getMeleeColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.meleeColorR, settings.meleeColorG, settings.meleeColorB, settings.meleeColorA);
	}

	// Explosive ESP Colors
	ImU32 DMARender::RenderBridge::getExplosiveColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.explosiveColorR, settings.explosiveColorG, settings.explosiveColorB, settings.explosiveColorA);
	}

	// Container ESP Colors
	ImU32 DMARender::RenderBridge::getContainerColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.containerColorR, settings.containerColorG, settings.containerColorB, settings.containerColorA);
	}

	// Cooking ESP Colors
	ImU32 DMARender::RenderBridge::getCookingColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.cookingColorR, settings.cookingColorG, settings.cookingColorB, settings.cookingColorA);
	}

	// Camping ESP Colors
	ImU32 DMARender::RenderBridge::getCampingColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.campingColorR, settings.campingColorG, settings.campingColorB, settings.campingColorA);
	}

	// Stash ESP Colors
	ImU32 DMARender::RenderBridge::getStashColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.stashColorR, settings.stashColorG, settings.stashColorB, settings.stashColorA);
	}

	// Ground Item ESP Colors
	ImU32 DMARender::RenderBridge::getGroundItemColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.groundItemColorR, settings.groundItemColorG, settings.groundItemColorB, settings.groundItemColorA);
	}

	// Crosshair Colors
	ImU32 DMARender::RenderBridge::getCrosshairColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.crosshairColorR, settings.crosshairColorG, settings.crosshairColorB, settings.crosshairColorA);
	}

	ImU32 DMARender::RenderBridge::getCrosshairOutlineColor() const {
		auto settings = settingsManager->getSettings();
		return IM_COL32(settings.crosshairOutlineColorR, settings.crosshairOutlineColorG, settings.crosshairOutlineColorB, settings.crosshairOutlineColorA);
	}


