#include "IOverlay.h"
#include "../RenderWindow/RenderBridge/RenderBridge.h"
#include <cmath> // For cos and sin functions
#include <chrono> // For time calculations
#include <algorithm> // For std::min function

bool DMARender::IOverlay::WorldToScreen(const _D3DMATRIX& view_matrix, const DMARender::Vector3& position, DMARender::Vector2& outScreenPos, const float& renderWidth, const float& renderHeight)
{
	DMARender::Vector3 transform = { view_matrix._14, view_matrix._24, view_matrix._34 };
	DMARender::Vector3 right = { view_matrix._11, view_matrix._21, view_matrix._31 };
	DMARender::Vector3 up = { view_matrix._12, view_matrix._22, view_matrix._32 };

	float w = transform.Dot(position) + view_matrix._44;

	if (w < 0.099f)
		return false;

	float x = right.Dot(position) + view_matrix._41;
	float y = up.Dot(position) + view_matrix._42;

	outScreenPos.x = (renderWidth / 2) * (1.f + x / w);
	outScreenPos.y = (renderHeight / 2) * (1.f - y / w);

	return true;
}

void DMARender::IOverlay::drawBoundingBox(const Vector2& top, const Vector2& bottom, float width, const ImU32& color) {
	// Use background draw list to draw over entire screen including taskbar
	auto drawList = ImGui::GetBackgroundDrawList();

	// Get window position for draggable overlay support
	auto windowPos = ImGui::GetWindowPos();

	// Get bounding box settings from bridge (if available)
	float boxThickness = 1.5f;  // Default thickness
	bool boxAntiAliasing = true; // Default anti-aliasing enabled
	bool boxRounded = false;     // Default rounded corners disabled
	float boxRounding = 2.0f;    // Default rounding radius
	int boxType = 0;             // Default box type (0: Normal, 1: Slim, 2: Corner)

	if (renderBridge) {
		try {
			boxThickness = renderBridge->getBoundingBoxThickness();
			boxAntiAliasing = renderBridge->getBoundingBoxAntiAliasing();
			boxRounded = renderBridge->getBoundingBoxRounded();
			boxRounding = renderBridge->getBoundingBoxRounding();
			boxType = renderBridge->getBoundingBoxType();
		}
		catch (...) {
			// Use defaults if bridge access fails
		}
	}

	// Calculate bounding box corners with window offset
	ImVec2 topLeft = ImVec2(windowPos.x + top.x - width / 2, windowPos.y + top.y);
	ImVec2 bottomRight = ImVec2(windowPos.x + bottom.x + width / 2, windowPos.y + bottom.y);
	ImVec2 topRight = ImVec2(bottomRight.x, topLeft.y);
	ImVec2 bottomLeft = ImVec2(topLeft.x, bottomRight.y);

	// Draw different box types based on CS2-style implementation
	switch (boxType) {
	case 0: // Normal Box (CS2-style)
		{
			// Use high-quality rectangle drawing with anti-aliasing
			if (boxRounded) {
				drawList->AddRect(topLeft, bottomRight, color, boxRounding, 0, boxThickness);
			} else {
				drawList->AddRect(topLeft, bottomRight, color, 0.0f, 0, boxThickness);
			}
		}
		break;

	case 1: // Slim Box (CS2-style bone-based)
		{
			// Calculate slim box dimensions (tighter fit)
			float height = bottomRight.y - topLeft.y;
			float slimWidth = height * 0.4f; // 40% of height for slim box
			
			ImVec2 slimTopLeft = ImVec2(topLeft.x + (width - slimWidth) / 2, topLeft.y);
			ImVec2 slimBottomRight = ImVec2(slimTopLeft.x + slimWidth, bottomRight.y);
			
			if (boxRounded) {
				drawList->AddRect(slimTopLeft, slimBottomRight, color, boxRounding, 0, boxThickness);
			} else {
				drawList->AddRect(slimTopLeft, slimBottomRight, color, 0.0f, 0, boxThickness);
			}
		}
		break;

	case 2: // Corner Box (CS2-style corner indicators)
		{
			float cornerLength = (width < (bottomRight.y - topLeft.y) ? width : (bottomRight.y - topLeft.y)) * 0.2f; // 20% of smallest dimension
			float cornerThickness = boxThickness * 1.5f; // Slightly thicker corners
			
			// Top-left corner
			drawList->AddLine(ImVec2(topLeft.x, topLeft.y), ImVec2(topLeft.x + cornerLength, topLeft.y), color, cornerThickness);
			drawList->AddLine(ImVec2(topLeft.x, topLeft.y), ImVec2(topLeft.x, topLeft.y + cornerLength), color, cornerThickness);
			
			// Top-right corner
			drawList->AddLine(ImVec2(topRight.x - cornerLength, topRight.y), ImVec2(topRight.x, topRight.y), color, cornerThickness);
			drawList->AddLine(ImVec2(topRight.x, topRight.y), ImVec2(topRight.x, topRight.y + cornerLength), color, cornerThickness);
			
			// Bottom-left corner
			drawList->AddLine(ImVec2(bottomLeft.x, bottomLeft.y - cornerLength), ImVec2(bottomLeft.x, bottomLeft.y), color, cornerThickness);
			drawList->AddLine(ImVec2(bottomLeft.x, bottomLeft.y), ImVec2(bottomLeft.x + cornerLength, bottomLeft.y), color, cornerThickness);
			
			// Bottom-right corner
			drawList->AddLine(ImVec2(bottomRight.x - cornerLength, bottomRight.y), ImVec2(bottomRight.x, bottomRight.y), color, cornerThickness);
			drawList->AddLine(ImVec2(bottomRight.x, bottomRight.y - cornerLength), ImVec2(bottomRight.x, bottomRight.y), color, cornerThickness);
		}
		break;

	case 3: // 3D Box (CS2-style with depth)
		{
			// Calculate 3D effect with depth
			float depth = width * 0.3f; // 30% of width for depth
			
			// Front face (original box)
			if (boxRounded) {
				drawList->AddRect(topLeft, bottomRight, color, boxRounding, 0, boxThickness);
			} else {
				drawList->AddRect(topLeft, bottomRight, color, 0.0f, 0, boxThickness);
			}
			
			// Back face (offset)
			ImVec2 backTopLeft = ImVec2(topLeft.x + depth, topLeft.y - depth);
			ImVec2 backBottomRight = ImVec2(bottomRight.x + depth, bottomRight.y - depth);
			
			// Draw back face with reduced alpha
			ImU32 backColor = IM_COL32(
				(color >> 0) & 0xFF,
				(color >> 8) & 0xFF,
				(color >> 16) & 0xFF,
				((color >> 24) & 0xFF) / 2 // Half alpha for back face
			);
			
			if (boxRounded) {
				drawList->AddRect(backTopLeft, backBottomRight, backColor, boxRounding, 0, boxThickness);
			} else {
				drawList->AddRect(backTopLeft, backBottomRight, backColor, 0.0f, 0, boxThickness);
			}
			
			// Connect front to back (depth lines)
			drawList->AddLine(topLeft, backTopLeft, backColor, boxThickness);
			drawList->AddLine(topRight, ImVec2(backTopLeft.x + width, backTopLeft.y), backColor, boxThickness);
			drawList->AddLine(bottomLeft, ImVec2(backTopLeft.x, backBottomRight.y), backColor, boxThickness);
			drawList->AddLine(bottomRight, backBottomRight, backColor, boxThickness);
		}
		break;

	default: // Fallback to normal box
		{
	if (boxRounded) {
		drawList->AddRect(topLeft, bottomRight, color, boxRounding, 0, boxThickness);
	} else {
		drawList->AddRect(topLeft, bottomRight, color, 0.0f, 0, boxThickness);
			}
		}
		break;
	}
}



void DMARender::IOverlay::drawFilledCircle(const Vector2& point, const float& radius, const ImU32& color) {
	// Use background draw list to draw over entire screen including taskbar
	auto drawList = ImGui::GetBackgroundDrawList();

	// Get window position for draggable overlay support
	auto windowPos = ImGui::GetWindowPos();

	// Apply window offset for draggable support
	drawList->AddCircleFilled(ImVec2(windowPos.x + point.x, windowPos.y + point.y), radius, color, 16);
}

void DMARender::IOverlay::drawText(const std::string& text, const Vector2& point, const float& size, const ImU32& color) {
	// Use background draw list to draw over entire screen including taskbar
	auto drawList = ImGui::GetBackgroundDrawList();

	// Get window position for draggable overlay support
	auto windowPos = ImGui::GetWindowPos();
	ImVec2 textPos = ImVec2(windowPos.x + point.x, windowPos.y + point.y);

	// Get font style settings from bridge (if available)
	bool fontOutline = false; // Default to outline DISABLED for zero lag
	bool fontShadow = false;  // Default to shadow DISABLED (causes black spots in fuser)
	bool fontBold = false;    // Default to bold disabled
	float fontAlpha = 1.0f;   // Default to full opacity
	float fontShadowOffset = 1.0f; // Default shadow offset
	float fontOutlineSize = 1.5f;  // Default outline size

	if (renderBridge) {
		try {
			fontOutline = renderBridge->getFontOutline();
			fontShadow = renderBridge->getFontShadow();
			fontBold = renderBridge->getFontBold();
			fontAlpha = renderBridge->getFontAlpha();
			fontShadowOffset = renderBridge->getFontShadowOffset();
			fontOutlineSize = renderBridge->getFontOutlineSize();
		}
		catch (...) {
			// Use defaults if bridge access fails
		}
	}

	// Apply alpha transparency to the main text color
	ImU32 alphaColor = applyAlphaToColor(color, fontAlpha);

	// FUSER-COMPATIBLE SHADOW: Dark gray instead of black to avoid transparency issues
	if (fontShadow) {
		ImU32 shadowColor = IM_COL32(64, 64, 64, 120); // Dark gray shadow - NO BLACK to avoid fuser issues
		drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x + fontShadowOffset, textPos.y + fontShadowOffset), shadowColor, text.c_str());
	}

	// FUSER-COMPATIBLE OUTLINE: Use multiple lighter outlines instead of single dark one
	if (fontOutline) {
		ImU32 outlineColor = IM_COL32(128, 128, 128, 100); // Very light gray with low opacity - Fuser compatible

		// Use multiple very light outlines instead of one dark one
		float outlineOffset = fontOutlineSize * 0.5f; // Smaller offset
		drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x - outlineOffset, textPos.y), outlineColor, text.c_str());
		drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x + outlineOffset, textPos.y), outlineColor, text.c_str());
		drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x, textPos.y - outlineOffset), outlineColor, text.c_str());
		drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x, textPos.y + outlineOffset), outlineColor, text.c_str());
	}

	// SIMPLIFIED BOLD: Single draw call for minimal lag
	if (fontBold) {
		float boldOffset = 0.5f;
		// Single additional draw call for bold effect - minimal lag
		drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x + boldOffset, textPos.y), alphaColor, text.c_str());
	}

	// Draw main text on top
	drawList->AddText(ImGui::GetFont(), size, textPos, alphaColor, text.c_str());
}

void DMARender::IOverlay::drawTextList(const std::vector<std::string>& strings, const Vector2& pos, const float& size, const ImU32& color) {
	int offsetCounter = 0;
	// Use background draw list to draw over entire screen including taskbar
	auto drawList = ImGui::GetBackgroundDrawList();

	// Get window position for draggable overlay support
	auto windowPos = ImGui::GetWindowPos();

	// Get font style settings from bridge (if available)
	bool fontOutline = false; // Default to outline DISABLED for zero lag
	bool fontShadow = false;  // Default to shadow DISABLED (causes black spots in fuser)
	bool fontBold = false;    // Default to bold disabled
	float fontAlpha = 1.0f;   // Default to full opacity
	float fontShadowOffset = 1.0f; // Default shadow offset
	float fontOutlineSize = 1.5f;  // Default outline size

	if (renderBridge) {
		try {
			fontOutline = renderBridge->getFontOutline();
			fontShadow = renderBridge->getFontShadow();
			fontBold = renderBridge->getFontBold();
			fontAlpha = renderBridge->getFontAlpha();
			fontShadowOffset = renderBridge->getFontShadowOffset();
			fontOutlineSize = renderBridge->getFontOutlineSize();
		}
		catch (...) {
			// Use defaults if bridge access fails
		}
	}

	// Apply alpha transparency to the main text color
	ImU32 alphaColor = applyAlphaToColor(color, fontAlpha);

	for (const std::string& str : strings) {
		// Calculate text position with window offset
		ImVec2 textPos = ImVec2(windowPos.x + pos.x, windowPos.y + pos.y + offsetCounter);

		// FUSER-COMPATIBLE SHADOW: Dark gray instead of black to avoid transparency issues
		if (fontShadow) {
			ImU32 shadowColor = IM_COL32(64, 64, 64, 120); // Dark gray shadow - NO BLACK to avoid fuser issues
			drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x + fontShadowOffset, textPos.y + fontShadowOffset), shadowColor, str.c_str());
		}

		// FUSER-COMPATIBLE OUTLINE: Use multiple lighter outlines instead of single dark one
		if (fontOutline) {
			ImU32 outlineColor = IM_COL32(128, 128, 128, 100); // Very light gray with low opacity - Fuser compatible

			// Use multiple very light outlines instead of one dark one
			float outlineOffset = fontOutlineSize * 0.5f; // Smaller offset
			drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x - outlineOffset, textPos.y), outlineColor, str.c_str());
			drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x + outlineOffset, textPos.y), outlineColor, str.c_str());
			drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x, textPos.y - outlineOffset), outlineColor, str.c_str());
			drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x, textPos.y + outlineOffset), outlineColor, str.c_str());
		}

		// SIMPLIFIED BOLD: Single draw call for minimal lag
		if (fontBold) {
			float boldOffset = 0.5f;
			// Single additional draw call for bold effect - minimal lag
			drawList->AddText(ImGui::GetFont(), size, ImVec2(textPos.x + boldOffset, textPos.y), alphaColor, str.c_str());
		}

		// Draw main text on top
		drawList->AddText(ImGui::GetFont(), size, textPos, alphaColor, str.c_str());

		offsetCounter += size;
	}
}

void DMARender::IOverlay::drawCrosshair() {
	if (!renderBridge) return;

	// Get crosshair settings from bridge
	bool crosshairEnabled = renderBridge->isCrosshairEnabled();
	if (!crosshairEnabled) return;

	float crosshairSize = renderBridge->getCrosshairSize();
	float crosshairThickness = renderBridge->getCrosshairThickness();
	float crosshairGap = renderBridge->getCrosshairGap();
	float crosshairAlpha = renderBridge->getCrosshairAlpha();
	bool crosshairOutline = renderBridge->isCrosshairOutline();
	float crosshairOutlineThickness = renderBridge->getCrosshairOutlineThickness();
	bool crosshairDot = renderBridge->isCrosshairDot();
	float crosshairDotSize = renderBridge->getCrosshairDotSize();
	float crosshairVerticalOffset = renderBridge->getCrosshairVerticalOffset();

	// Get colors
	ImU32 crosshairColor = renderBridge->getCrosshairColor();
	ImU32 outlineColor = renderBridge->getCrosshairOutlineColor();

	// Apply alpha to colors
	ImU32 alphaCrosshairColor = applyAlphaToColor(crosshairColor, crosshairAlpha);
	ImU32 alphaOutlineColor = applyAlphaToColor(outlineColor, crosshairAlpha);

	// Get screen center
	auto io = ImGui::GetIO();
	float screenCenterX = io.DisplaySize.x * 0.5f;
	float screenCenterY = io.DisplaySize.y * 0.5f;

	// Use background draw list to draw over entire screen
	auto drawList = ImGui::GetBackgroundDrawList();

	// Get window position for draggable overlay support
	auto windowPos = ImGui::GetWindowPos();

	// Calculate crosshair position
	float centerX, centerY;
	bool useAimPoint = renderBridge->isCrosshairUseAimPoint();
	
	if (useAimPoint) {
		// Try to calculate aim point
		float aimScreenX, aimScreenY;
		if (calculateAimPoint(aimScreenX, aimScreenY)) {
			centerX = windowPos.x + aimScreenX;
			centerY = windowPos.y + aimScreenY;
		} else {
			// Fallback to screen center if aim point calculation fails
			centerX = windowPos.x + screenCenterX;
			centerY = windowPos.y + screenCenterY;
		}
	} else {
		// Use screen center
		centerX = windowPos.x + screenCenterX;
		centerY = windowPos.y + screenCenterY;
	}

	// Apply vertical offset
	centerY += crosshairVerticalOffset;

	// Calculate crosshair line positions
	float leftStart = centerX - crosshairGap - crosshairSize;
	float leftEnd = centerX - crosshairGap;
	float rightStart = centerX + crosshairGap;
	float rightEnd = centerX + crosshairGap + crosshairSize;
	
	float topStart = centerY - crosshairGap - crosshairSize;
	float topEnd = centerY - crosshairGap;
	float bottomStart = centerY + crosshairGap;
	float bottomEnd = centerY + crosshairGap + crosshairSize;

	// Draw outline first (if enabled)
	if (crosshairOutline) {
		// Draw multiple outline lines for better visibility
		float outlineOffset = crosshairOutlineThickness;
		
		// Horizontal line outline (left) - multiple lines for better outline
		drawList->AddLine(
			ImVec2(leftStart, centerY - outlineOffset),
			ImVec2(leftEnd, centerY - outlineOffset),
			alphaOutlineColor,
			crosshairThickness
		);
		drawList->AddLine(
			ImVec2(leftStart, centerY + outlineOffset),
			ImVec2(leftEnd, centerY + outlineOffset),
			alphaOutlineColor,
			crosshairThickness
		);
		
		// Horizontal line outline (right) - multiple lines for better outline
		drawList->AddLine(
			ImVec2(rightStart, centerY - outlineOffset),
			ImVec2(rightEnd, centerY - outlineOffset),
			alphaOutlineColor,
			crosshairThickness
		);
		drawList->AddLine(
			ImVec2(rightStart, centerY + outlineOffset),
			ImVec2(rightEnd, centerY + outlineOffset),
			alphaOutlineColor,
			crosshairThickness
		);

		// Vertical line outline (top) - multiple lines for better outline
		drawList->AddLine(
			ImVec2(centerX - outlineOffset, topStart),
			ImVec2(centerX - outlineOffset, topEnd),
			alphaOutlineColor,
			crosshairThickness
		);
		drawList->AddLine(
			ImVec2(centerX + outlineOffset, topStart),
			ImVec2(centerX + outlineOffset, topEnd),
			alphaOutlineColor,
			crosshairThickness
		);
		
		// Vertical line outline (bottom) - multiple lines for better outline
		drawList->AddLine(
			ImVec2(centerX - outlineOffset, bottomStart),
			ImVec2(centerX - outlineOffset, bottomEnd),
			alphaOutlineColor,
			crosshairThickness
		);
		drawList->AddLine(
			ImVec2(centerX + outlineOffset, bottomStart),
			ImVec2(centerX + outlineOffset, bottomEnd),
			alphaOutlineColor,
			crosshairThickness
		);
	}

	// Draw main crosshair lines
	// Horizontal line (left)
	drawList->AddLine(
		ImVec2(leftStart, centerY),
		ImVec2(leftEnd, centerY),
		alphaCrosshairColor,
		crosshairThickness
	);
	// Horizontal line (right)
	drawList->AddLine(
		ImVec2(rightStart, centerY),
		ImVec2(rightEnd, centerY),
		alphaCrosshairColor,
		crosshairThickness
	);

	// Vertical line (top)
	drawList->AddLine(
		ImVec2(centerX, topStart),
		ImVec2(centerX, topEnd),
		alphaCrosshairColor,
		crosshairThickness
	);
	// Vertical line (bottom)
	drawList->AddLine(
		ImVec2(centerX, bottomStart),
		ImVec2(centerX, bottomEnd),
		alphaCrosshairColor,
		crosshairThickness
	);

	// Draw center dot (if enabled)
	if (crosshairDot) {
		if (crosshairOutline) {
			// Draw outline for dot
			drawList->AddCircleFilled(
				ImVec2(centerX, centerY),
				crosshairDotSize + crosshairOutlineThickness,
				alphaOutlineColor
			);
		}
		// Draw main dot
		drawList->AddCircleFilled(
			ImVec2(centerX, centerY),
			crosshairDotSize,
			alphaCrosshairColor
		);
	}
}

ImU32 DMARender::IOverlay::applyAlphaToColor(const ImU32& color, float alpha) const {
	// Extract RGBA components from the color
	int r = (color >> 0) & 0xFF;    // Red component
	int g = (color >> 8) & 0xFF;    // Green component
	int b = (color >> 16) & 0xFF;   // Blue component
	int a = (color >> 24) & 0xFF;   // Alpha component
	
	// Apply alpha multiplier to the existing alpha
	int newAlpha = (int)(a * alpha);
	
	// Clamp alpha to valid range (0-255)
	if (newAlpha < 0) newAlpha = 0;
	if (newAlpha > 255) newAlpha = 255;
	
	// Reconstruct the color with new alpha
	return IM_COL32(r, g, b, newAlpha);
}

bool DMARender::IOverlay::calculateAimPoint(float& screenX, float& screenY) {
	// Try to use the derived class's real aim point calculation first
	if (calculateRealAimPoint(screenX, screenY)) {
		return true;
	}
	
	// Fallback to RenderBridge method if derived class doesn't implement it
	if (renderBridge) {
		return renderBridge->calculateAimPoint(screenX, screenY);
	}
	
	return false;
}