#pragma once
#include "DMAMemoryManagement/includes.h"
#include "DMARender/includes.h"
#include "Entity.h"
#include "Camera.h"
#include <vector>
#include <unordered_map>
#include <chrono>
#include <mutex>
#include <thread>
#include <future>
#include <queue>
#include <atomic>

namespace DayZ {

    // ===== BONE CONNECTION STRUCTURE =====
    struct BoneConnection {
        uint32_t from;
        uint32_t to;
    };

    // ===== MATRIX STRUCTURE FOR BONE CALCULATIONS =====
    struct Matrix4x4 {
        float m[12];
    };

    // ===== BATCHED BONE DATA STRUCTURE =====
    struct BatchedBoneData {
        std::vector<DMARender::Vector3> bonePositions;
        std::unordered_map<uint32_t, size_t> boneIndexMap; // boneId -> index in bonePositions
        bool isValid;
        std::chrono::steady_clock::time_point timestamp;
    };

    // ===== BATCHED SCREEN POSITION DATA STRUCTURE =====
    struct BatchedScreenData {
        std::vector<DMARender::Vector2> screenPositions;
        std::vector<bool> isVisible;
        std::unordered_map<uint32_t, size_t> boneIndexMap; // boneId -> index in screenPositions
        bool isValid;
        std::chrono::steady_clock::time_point timestamp;
    };

    // ===== SKELETON CACHE STRUCTURES =====
    struct CachedBoneMatrices {
        std::vector<Matrix4x4> boneMatrices;
        Matrix4x4 visStateMatrix;
        uintptr_t skeletonAddress;
        uintptr_t visStateAddress;
        std::chrono::steady_clock::time_point timestamp;
        bool isValid;
        size_t maxBoneId;
    };

    struct SkeletonCacheEntry {
        uint32_t entityNetworkId;
        CachedBoneMatrices boneMatrices;
        BatchedBoneData bonePositions;
        BatchedScreenData screenPositions;
        std::chrono::steady_clock::time_point lastUpdate;
        bool isPlayer;
        bool isValid;
    };

    // ===== MULTITHREADED SKELETON PROCESSING STRUCTURES =====
    struct SkeletonWorkItem {
        std::shared_ptr<DayZ::Entity> entity;
        bool isPlayer;
        bool isZombie;
        uint32_t networkId;
        float distance;
        ImU32 color;
        float thickness;
        int detailLevel;
        bool shouldProcess;
    };

    struct SkeletonWorkResult {
        uint32_t networkId;
        bool isPlayer;
        bool isZombie;
        BatchedScreenData screenData;
        std::vector<DayZ::BoneConnection> connections;
        ImU32 color;
        float thickness;
        bool isValid;
        std::string errorMessage;
    };

    // ===== SKELETON CACHE CONSTANTS =====
    namespace SkeletonCacheConstants {
        static constexpr size_t MAX_CACHE_SIZE = 100; // Maximum cached entities (increased from 50)
        static constexpr std::chrono::milliseconds CACHE_DURATION{100}; // Cache for 100ms (10 FPS)
        static constexpr std::chrono::milliseconds MATRIX_CACHE_DURATION{500}; // Matrix cache for 500ms (2 FPS)
    }

    // ===== SKELETON CACHE MANAGER =====
    class SkeletonCache {
    public:
        static constexpr size_t MAX_CACHE_SIZE = SkeletonCacheConstants::MAX_CACHE_SIZE;
        static constexpr std::chrono::milliseconds CACHE_DURATION = SkeletonCacheConstants::CACHE_DURATION;
        static constexpr std::chrono::milliseconds MATRIX_CACHE_DURATION = SkeletonCacheConstants::MATRIX_CACHE_DURATION;
        
    private:
        std::unordered_map<uint32_t, SkeletonCacheEntry> cache;
        std::mutex cacheMutex;
        
    public:
        // Cache management functions
        SkeletonCacheEntry* GetCachedSkeleton(uint32_t networkId);
        void UpdateSkeletonCache(uint32_t networkId, const SkeletonCacheEntry& entry);
        void ClearExpiredCache();
        void ClearAllCache();
        
        // Thread-safe access
        std::lock_guard<std::mutex> LockCache() { return std::lock_guard<std::mutex>(cacheMutex); }
    };

    // ===== MULTITHREADED SKELETON WORKER SYSTEM =====
    class SkeletonWorkerSystem {
    public:
        static constexpr size_t DEFAULT_WORKER_COUNT = 4; // Number of worker threads
        static constexpr size_t MAX_QUEUE_SIZE = 100; // Maximum work items in queue
        
    private:
        std::vector<std::thread> workers;
        std::queue<SkeletonWorkItem> workQueue;
        std::vector<SkeletonWorkResult> results;
        std::mutex queueMutex;
        std::mutex resultsMutex;
        std::condition_variable queueCondition;
        std::atomic<bool> shouldStop;
        std::atomic<size_t> activeWorkers;
        size_t workerCount;
        
        // Thread parameters
        DMAMem::VmmManager* currentVmm;
        DWORD currentPid;
        DayZ::Camera* currentCamera;
        
    public:
        SkeletonWorkerSystem(size_t numWorkers = DEFAULT_WORKER_COUNT);
        ~SkeletonWorkerSystem();
        
        // Thread management
        void StartWorkers(DMAMem::VmmManager* vmm = nullptr, DWORD pid = 0, DayZ::Camera* camera = nullptr);
        void StopWorkers();
        void WaitForCompletion();
        
        // Work distribution
        void AddWorkItem(const SkeletonWorkItem& item);
        void ProcessWorkItems(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera);
        
        // Results management
        std::vector<SkeletonWorkResult> GetResults();
        void ClearResults();
        
        // Status
        bool IsRunning() const { return !shouldStop; }
        size_t GetActiveWorkers() const { return activeWorkers; }
        size_t GetQueueSize() const;
        
    private:
        void WorkerThread();
        SkeletonWorkResult ProcessWorkItem(const SkeletonWorkItem& item, DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera);
    };

    // ===== PLAYER BONE IDS =====
    enum class PlayerBoneId : uint32_t {
        pelvis = 1,
        spine = 18,
        spine1 = 19,
        spine2 = 20,
        spine3 = 21,
        neck = 22,
        neck1 = 23,
        head = 24,
        
        // Left arm
        leftshoulder = 61,
        leftarm = 62,
        leftarmroll = 63,
        leftforearm = 64,
        leftforearmroll = 65,
        lefthand = 66,
        
        // Right arm
        rightshoulder = 94,
        rightarm = 95,
        rightarmroll = 97,
        rightforearm = 98,
        rightforearmroll = 99,
        righthand = 100,
        
        // Left leg
        leftupleg = 2,
        leftuplegroll = 3,
        leftleg = 5,
        leftkneeextra = 4,
        leftfoot = 7,
        lefttoebase = 8,
        
        // Right leg
        rightupleg = 10,
        rightuplegroll = 11,
        rightkneeextra = 12,
        rightleg = 13,
        rightlegroll = 14,
        rightfoot = 15,
        righttoebase = 16,
        
        // Enhanced hand details (from UnknownCheats)
        lefthandthumb1 = 80, lefthandthumb2 = 81, lefthandthumb3 = 82, lefthandthumb4 = 83,
        lefthandindex1 = 76, lefthandindex2 = 77, lefthandindex3 = 78, lefthandindex4 = 79,
        lefthandmiddle1 = 72, lefthandmiddle2 = 73, lefthandmiddle3 = 74, lefthandmiddle4 = 75,
        lefthandring1 = 68, lefthandring2 = 69, lefthandring3 = 70, lefthandring4 = 71,
        lefthandpinky1 = 85, lefthandpinky2 = 86, lefthandpinky3 = 87, lefthandpinky4 = 88,

        righthandthumb1 = 106, righthandthumb2 = 107, righthandthumb3 = 108, righthandthumb4 = 109,
        righthandindex1 = 114, righthandindex2 = 115, righthandindex3 = 116, righthandindex4 = 117,
        righthandmiddle1 = 110, righthandmiddle2 = 111, righthandmiddle3 = 112, righthandmiddle4 = 113,
        righthandring1 = 102, righthandring2 = 103, righthandring3 = 104, righthandring4 = 105,
        righthandpinky1 = 119, righthandpinky2 = 120, righthandpinky3 = 121, righthandpinky4 = 122,

        // Additional arm details
        leftarmextra = 93, leftelbowextra = 92, leftforearmextra = 91, leftwristextra = 90,
        rightarmextra = 96, rightelbowextra = 139, rightforearmextra = 138, rightwristextra = 137,

        // Additional body parts
        jaw = 7, reye = 16, rear = 15, lear = 8,
    };

    // ===== ZOMBIE/INFECTED BONE IDS =====
    enum class InfectedBoneId : uint32_t {
        pelvis = 1,
        spine = 16,
        spine1 = 17,
        spine2 = 18,
        spine3 = 19,
        neck = 20,
        neck1 = 21,
        head = 22,
        
        // Left arm
        leftshoulder = 24,
        leftarm = 25,
        leftarmroll = 26,
        leftforearm = 27,
        leftforearmroll = 28,
        lefthand = 29,
        
        // Right arm
        rightshoulder = 56,
        rightarm = 57,
        rightarmroll = 59,
        rightforearm = 60,
        rightforearmroll = 61,
        righthand = 62,
        
        // Left leg
        leftupleg = 2,
        leftuplegroll = 3,
        leftleg = 5,
        leftkneeextra = 4,
        leftfoot = 7,
        lefttoebase = 8,
        
        // Right leg
        rightupleg = 9,
        rightuplegroll = 10,
        rightkneeextra = 11,
        rightleg = 12,
        rightlegroll = 13,
        rightfoot = 14,
        righttoebase = 15,

        // Enhanced hand details (from UnknownCheats)
        lefthandthumb1 = 47, lefthandthumb2 = 48, lefthandthumb3 = 49, lefthandthumb4 = 50,
        lefthandindex1 = 43, lefthandindex2 = 44, lefthandindex3 = 45, lefthandindex4 = 46,
        lefthandmiddle1 = 39, lefthandmiddle2 = 40, lefthandmiddle3 = 41, lefthandmiddle4 = 42,
        lefthandring1 = 31, lefthandring2 = 32, lefthandring3 = 33, lefthandring4 = 34,
        lefthandpinky1 = 35, lefthandpinky2 = 36, lefthandpinky3 = 37, lefthandpinky4 = 38,

        righthandthumb1 = 72, righthandthumb2 = 73, righthandthumb3 = 74, righthandthumb4 = 75,
        righthandindex1 = 80, righthandindex2 = 81, righthandindex3 = 82, righthandindex4 = 83,
        righthandmiddle1 = 76, righthandmiddle2 = 77, righthandmiddle3 = 78, righthandmiddle4 = 79,
        righthandring1 = 68, righthandring2 = 69, righthandring3 = 70, righthandring4 = 71,
        righthandpinky1 = 64, righthandpinky2 = 65, righthandpinky3 = 66, righthandpinky4 = 67,

        // Additional arm details
        leftarmextra = 55, leftelbowextra = 54, leftforearmextra = 53, leftwristextra = 52,
        rightarmextra = 58, rightelbowextra = 87, rightforearmextra = 86, rightwristextra = 85,

        // Additional body parts
        jaw = 7, reye = 15, rear = 14, lear = 8,
    };



    // ===== SKELETON ESP CLASS =====
    class SkeletonESP {
    public:
        // ===== ORIGINAL FUNCTIONS (KEPT FOR COMPATIBILITY) =====
        // Core bone position function (1:1 rebuild from game)
        static bool GetBonePositionWS(DMAMem::VmmManager* vmm, DWORD pid, uintptr_t skeleton, uintptr_t visState, uint32_t pivot, DMARender::Vector3* pos);
        
        // Player skeleton functions
        static bool GetPlayerBonePosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity, PlayerBoneId boneId, DMARender::Vector3* pos);
        static std::vector<DMARender::Vector3> GetPlayerSkeleton(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity);

        // Zombie skeleton functions
        static bool GetZombieBonePosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity, InfectedBoneId boneId, DMARender::Vector3* pos);
        static std::vector<DMARender::Vector3> GetZombieSkeleton(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity);

        // ===== NEW BATCHED FUNCTIONS (HIGH PERFORMANCE) =====
        // Batched bone reading - reads ALL bones in single DMA operation
        static BatchedBoneData GetPlayerSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity);
        static BatchedBoneData GetZombieSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity);
        
        // Optimized rendering using batched data
        static void DrawPlayerSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 255, 255, 255), float thickness = 0.5f);
        static void DrawZombieSkeletonBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 0, 0, 255), float thickness = 0.5f);

        // ===== ULTRA-HIGH PERFORMANCE BATCHED FUNCTIONS =====
        // Batched WorldToScreen - calculates ALL bone screen positions at once
        static BatchedScreenData GetPlayerSkeletonScreenBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity);
        static BatchedScreenData GetZombieSkeletonScreenBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity);
        
        // Ultra-optimized rendering using pre-calculated screen positions
        static void DrawPlayerSkeletonUltraBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 255, 255, 255), float thickness = 0.5f, int detailLevel = 2);
        static void DrawZombieSkeletonUltraBatched(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 0, 0, 255), float thickness = 0.5f, int detailLevel = 2);

        // ===== CACHED SKELETON FUNCTIONS (ULTIMATE PERFORMANCE) =====
        // Cached skeleton rendering - reuses bone matrices for multiple frames
        static void DrawPlayerSkeletonCached(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 255, 255, 255), float thickness = 0.5f);
        static void DrawZombieSkeletonCached(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 0, 0, 255), float thickness = 0.5f);
        
        // Cache management
        static void ClearSkeletonCache();
        static void UpdateSkeletonCache();

        // ===== MULTITHREADED SKELETON PROCESSING FUNCTIONS =====
        // Multithreaded skeleton processing for multiple entities
        static void ProcessSkeletonsMultithreaded(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, 
                                                 const std::vector<std::shared_ptr<DayZ::Entity>>& entities,
                                                 bool showPlayerSkeleton, bool showZombieSkeleton,
                                                 float playerMaxDistance, float zombieMaxDistance,
                                                 ImU32 playerColor, ImU32 zombieColor, float thickness, int detailLevel);
        
        // Render pre-processed skeleton results
        static void RenderSkeletonResults(const std::vector<SkeletonWorkResult>& results);
        
        // Worker system management
        static void InitializeWorkerSystem(size_t workerCount = SkeletonWorkerSystem::DEFAULT_WORKER_COUNT);
        static void ShutdownWorkerSystem();
        static SkeletonWorkerSystem* GetWorkerSystem();

        // DayZ2-style rendering functions (high performance, thin lines)
        static void DrawPlayerSkeleton(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 255, 255, 255), float thickness = 0.5f);
        static void DrawZombieSkeleton(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 0, 0, 255), float thickness = 0.5f);

        // Utility functions
        static bool IsValidSkeleton(DMAMem::VmmManager* vmm, DWORD pid, uintptr_t skeleton);
        static DMARender::Vector3 GetHeadPosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity);
        static DMARender::Vector3 GetChestPosition(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity);

        // Performance optimization: Distance-based processing control
        static bool ShouldSkipSkeletonProcessing(DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, bool isPlayer, float maxDistance);

        // Internal rendering helper
        static void DrawBoneLine(DayZ::Camera* camera, const DMARender::Vector3& from, const DMARender::Vector3& to, ImU32 color, float thickness = 0.5f);
        
        // Head circle drawing (DayZ mod.txt style)
        static void DrawHeadCircle(DMAMem::VmmManager* vmm, DWORD pid, DayZ::Camera* camera, std::shared_ptr<DayZ::Entity> entity, ImU32 color = IM_COL32(255, 255, 255, 255), float thickness = 1.0f, float radiusMultiplier = 0.75f, float sizeMultiplier = 1.0f);

        // Detail level functions (public for worker system access)
        static std::vector<DayZ::BoneConnection> GetPlayerBoneConnections(int detailLevel);
        static std::vector<DayZ::BoneConnection> GetZombieBoneConnections(int detailLevel);
        
        // DayZ64 CheatsBuddy style skeleton (simple stick figure)
        static std::vector<DayZ::BoneConnection> GetCheatsBuddyPlayerBoneConnections();
        static std::vector<DayZ::BoneConnection> GetCheatsBuddyZombieBoneConnections();
        
    private:
        // Skeleton offsets
        static constexpr uintptr_t PLAYER_SKELETON_OFFSET = 0x7E8; // DayZPlayer::Skeleton
        static constexpr uintptr_t ZOMBIE_SKELETON_OFFSET = 0x678; // DayZInfected::Skeleton
        
        // Animation class offsets
        static constexpr uintptr_t ANIM_CLASS_OFFSET = 0xB0; // AnimClass::AnimComponent (updated from 0xA8)
        static constexpr uintptr_t MATRIX_CLASS_OFFSET = 0xBF0; // AnimClass::MatrixArray
        
        // Bone connections for drawing lines (using top-level BoneConnection struct)

        // DayZ2-style bone connections (lightweight, high performance)
        static const std::vector<DayZ::BoneConnection> PLAYER_BONE_CONNECTIONS; // DayZ2-style minimal
        static const std::vector<DayZ::BoneConnection> ZOMBIE_BONE_CONNECTIONS; // DayZ2-style minimal

        // Helper functions
        static bool WorldToScreen(DayZ::Camera* camera, const DMARender::Vector3& worldPos, DMARender::Vector2* screenPos);
        
        // ===== BATCHED BONE READING HELPERS =====
        // Core batched bone reading function
        static bool GetBonePositionsBatched(DMAMem::VmmManager* vmm, DWORD pid, uintptr_t skeleton, uintptr_t visState, 
                                           const std::vector<uint32_t>& boneIds, BatchedBoneData* result);
        
        // Get all bone IDs needed for skeleton rendering
        static std::vector<uint32_t> GetPlayerBoneIds();
        static std::vector<uint32_t> GetZombieBoneIds();
        
        // ===== BATCHED WORLD TO SCREEN HELPERS =====
        // Core batched WorldToScreen function - calculates ALL positions at once
        static bool WorldToScreenBatched(DayZ::Camera* camera, const std::vector<DMARender::Vector3>& worldPositions, 
                                        BatchedScreenData* result);
        
        // ===== SKELETON CACHE HELPERS =====
        // Core cached bone reading function - reuses matrices for multiple frames
        static bool GetBonePositionsCached(DMAMem::VmmManager* vmm, DWORD pid, std::shared_ptr<DayZ::Entity> entity,
                                          const std::vector<uint32_t>& boneIds, SkeletonCacheEntry* cacheEntry);
        
        // Cache management helpers
        static SkeletonCache skeletonCache;
        
        // Worker system instance
        static std::unique_ptr<SkeletonWorkerSystem> workerSystem;
    };



} // namespace DayZ
