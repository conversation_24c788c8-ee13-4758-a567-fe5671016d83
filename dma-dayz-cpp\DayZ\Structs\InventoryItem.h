#pragma once
#include "DMAMemoryManagement/includes.h";
#include "FutureVisualState.h";
#include "EntityType.h"
#include "Scoreboard.h"
#include "ScoreboardIdentity.h"
#include "EntityInventory.h"
#include "EntityFilterList.h"


namespace DayZ {


	class InventoryItem : public DMAMem::MemoryObject {

		bool _isValid = false;
		bool _isValidChecked = false;

	public:

		std::shared_ptr<EntityType> EntityTypePtr;


		InventoryItem() {
			EntityTypePtr = std::shared_ptr<EntityType>(new EntityType());
			this->registerPointer(0x180, EntityTypePtr.get());
		}

		bool isValid() {

			if (!_isValidChecked) {
				// Validate entity type strings FIRST to prevent null pointer access
				if (!EntityTypePtr || !EntityTypePtr->TypeName || !EntityTypePtr->TypeName->value) {
					_isValid = false;
					_isValidChecked = true;
					return _isValid;
				}

				// Check for blacklisted items using the same logic as Entity class (getBestString)
				auto bestString = this->EntityTypePtr->getBestString();
				if (bestString && bestString->value) {
					std::string entityName = bestString->value;
					if (itemBlacklistSet.find(entityName) != itemBlacklistSet.end()) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
				}

				// Also check TypeName directly as fallback (for compatibility)
				if (this->EntityTypePtr->TypeName && this->EntityTypePtr->TypeName->value) {
					std::string entityTypeName = this->EntityTypePtr->TypeName->value;
					if (itemBlacklistSet.find(entityTypeName) != itemBlacklistSet.end()) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
				}

				_isValid = this->EntityTypePtr->TypeName->length < 400 && this->EntityTypePtr->TypeName->length > 0;
				_isValidChecked = true;
			}
			return _isValid;
		}

	};
}