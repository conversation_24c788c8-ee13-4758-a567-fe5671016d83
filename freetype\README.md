FreeType 2.13.3
=========================
###### FreeType Windows libraries (win32/win64)
Compiled with VS 2022 (SDK 10.0.26100)
> The *freetype* libraries use the *Universal CRT* and therefore **_requires_** Visual C++ 2015-2022 Redistributable to be present on system.
###### Supported operating systems
- Windows 10 version 1507 or higher
- Windows Server 2022, Windows Server 2019, Windows Server 2016 and Windows Server 2012 R2
- Windows 8.1
- Windows 7 SP1
###### Hash values for DLL files
| filename | sha256 |
| :-- | :-- |
| release dll\\win32\\**freetype.dll** | `F8F398B7E4F0D45348520FF4A7CC05FB81B6E241A3067B9D85D5D381E1FB1E7A` |
| release dll\\win32\\**freetype.lib** | `0B69476D66A32F22C1FF3DFBEFE1984DE6292159D36BD2ECC8144A26C76B2FAE` |
| release dll\\win64\\**freetype.dll** | `48267DF7F4619AB4B12A8EE75D513C4C378A30BFEC5C48CBABCB678B72F8F697` |
| release dll\\win64\\**freetype.lib** | `869E821E5072B507F06B36640336E08B9F8FCD65A3A63C6C27A55B0F33599AB1` |
###### Hash values for static library files
| filename | sha256 |
| :-- | :-- |
| release static\\vs2015-2022\\win32\\**freetype.lib** | `58391E31438D7182A62596C48CCB5E0C6B76B8F37B7EE6565BADF3564D1465F8` |
| release static\\vs2015-2022\\win64\\**freetype.lib** | `74856E22714BF99F2F04E59CFE77D7C85729E5AA3570030B939048115EF528AD` |
