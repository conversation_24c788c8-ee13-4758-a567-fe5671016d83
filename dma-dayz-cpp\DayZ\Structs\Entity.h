#pragma once
#include "DMAMemoryManagement/includes.h";
#include "FutureVisualState.h";
#include "EntityType.h"
#include "Scoreboard.h"
#include "ScoreboardIdentity.h"
#include "EntityInventory.h"
#include "EntityFilterList.h"
#include <vector>


namespace DayZ {


	enum ENTITY_TYPE {
		NONE,
		GROUNDITEM,
		PLAYER,
		ZOMBIE,
		CAR,
		BOAT,
		ANIMAL,
		CLOTHING,
		WEAPON,
		PR<PERSON><PERSON><PERSON><PERSON><PERSON>IN<PERSON>,
		BACKPACK,
		FOOD,
		AMMO,
		RARE,
		OPTICS,
		BASE,
		MELEE,
		EXPLOSIVES,
		CONTAINER,  // New: For storage containers
		COOKING,    // New: For cooking items that can hold cargo
		CAMPING,    // New: For camping items that can hold cargo
		STASH,      // New: For stash containers
		UNSORTED    // New: For items that don't match any specific category (DayZ mod style)
	};

	class Entity : public DMAMem::MemoryObject {

		ENTITY_TYPE _entityType = ENTITY_TYPE::NONE;
		bool _isEntityTypeChecked = false;
		bool _isValid = false;
		bool _isValidChecked = false;

		// Hand item caching for performance optimization
		std::string _cachedHandItemName = "";
		uint32_t _cachedHandItemFrame = 0;
		bool _cachedHandItemValid = false;

		// static list for rare items
		static std::set<std::string> rareItems;
		static void loadRareItems(const std::string& filePath);

	public:
		static void initializeRareItems(const std::string& filePath) {
			loadRareItems(filePath);
		}

		// Add an item to the rare items list (in-memory only, LootListManager handles file I/O)
		static void addToRareItems(const std::string& itemName) {
			rareItems.insert(itemName);
			// Note: File I/O is handled by LootListManager to avoid conflicts
		}

		// Get the rare items set for external access
		static const std::set<std::string>& getRareItems() {
			return rareItems;
		}

		// Set the rare items from external source (like LootListManager)
		static void setRareItems(const std::set<std::string>& newRareItems) {
			rareItems = newRareItems;
		}

		// categorize entity, casesensitive
		void categorizeEntity() {
			if (_entityType == ENTITY_TYPE::NONE) {
				if (!strcmp("dayzplayer", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::PLAYER;
					return;
				}
				if (!strcmp("dayzinfected", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::ZOMBIE;
					return;
				}
				if (!strcmp("car", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::CAR;
					return;
				}
				if (!strcmp("boat", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::BOAT;
					return;
				}
				if (!strcmp("dayzanimal", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::ANIMAL;
					return;
				}
				if (rareItems.find(this->EntityTypePtr->TypeName->value) != rareItems.end()) {
					_entityType = ENTITY_TYPE::RARE;
					return;
				}
				if (strstr(this->EntityTypePtr->ModelName->value, "backpacks") != NULL) {
					_entityType = ENTITY_TYPE::BACKPACK;
					return;
				}
				if (!strcmp("clothing", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::CLOTHING;
					return;
				}
				if (!strcmp("ProxyMagazines", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::PROXYMAGAZINES;
					return;
				}
				if (strstr(this->EntityTypePtr->ModelName->value, "food") != NULL) {
					_entityType = ENTITY_TYPE::FOOD;
					return;
				}
				if (strstr(this->EntityTypePtr->ModelName->value, "ammunition") != NULL) {
					_entityType = ENTITY_TYPE::AMMO;
					return;
				}
			if (strstr(this->EntityTypePtr->ModelName->value, "firearms") != NULL ||
				strcmp("Weapon", this->EntityTypePtr->ConfigName->value) == 0) {
				_entityType = ENTITY_TYPE::WEAPON;
				return;
			}
			if (!strcmp("itemoptics", this->EntityTypePtr->ConfigName->value)) {
				_entityType = ENTITY_TYPE::OPTICS;
				return;
			}
			// Check for camping items (DayZ mod style - check model paths and config names)
			if (strstr(this->EntityTypePtr->ModelName->value, "camping") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "\\dz\\gear\\camping") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "/dz/gear/camping") != NULL ||
				// Check for specific camping items by config name
				!strcmp("CombinationLock", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Tent_Medium", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Tent_Large", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("TentPegs", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Tarp", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("ShelterStick", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("ShelterLeather", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("ShelterFabric", this->EntityTypePtr->ConfigName->value)) {
				_entityType = ENTITY_TYPE::CAMPING;
				return;
			}
			if (strstr(this->EntityTypePtr->ModelName->value, "melee") != NULL) {
				_entityType = ENTITY_TYPE::MELEE;
				return;
			}
			if (strstr(this->EntityTypePtr->ModelName->value, "explosives") != NULL) {
				_entityType = ENTITY_TYPE::EXPLOSIVES;
				return;
			}

			// Check for cooking items that can hold cargo (check this BEFORE containers)
			if (strstr(this->EntityTypePtr->ModelName->value, "cooking") != NULL) {
				_entityType = ENTITY_TYPE::COOKING;
				return;
			}



			// Check for stash containers (DayZ mod style - inheritance and model paths)
			if (strstr(this->EntityTypePtr->ModelName->value, "stash") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "underground") != NULL ||
				// Check for specific stash items by config name (DayZ mod inheritance style)
				!strcmp("UndergroundStash", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("SeaChest", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("WoodenCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("ProtectorCase", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("AmmoBox", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("FirstAidKit", this->EntityTypePtr->ConfigName->value)) {
				_entityType = ENTITY_TYPE::STASH;
				return;
			}


			// Check for containers that can hold cargo (DayZ mod style - inheritance and model paths)
			if (strstr(this->EntityTypePtr->ModelName->value, "containers") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "\\dz\\gear\\containers") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "/dz/gear/containers") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "storage") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "ammo_crate") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "weapon_crate") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "food_crate") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "medical_crate") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "tool_crate") != NULL ||
				strstr(this->EntityTypePtr->ModelName->value, "building_crate") != NULL ||
				// Check config names for containers (DayZ mod inheritance style)
				!strcmp("Container_Base", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("RA_SkinnableCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("RA_WoodenCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("StorageBox", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("AmmoCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("WeaponCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("FoodCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("MedicalCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("ToolCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("BuildingCrate", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Barrel_ColorBase", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Barrel_Green", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Barrel_Blue", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Barrel_Red", this->EntityTypePtr->ConfigName->value) ||
				!strcmp("Barrel_Yellow", this->EntityTypePtr->ConfigName->value)) {
				_entityType = ENTITY_TYPE::CONTAINER;
				return;
			}

				// Check for basebuilding items (check this BEFORE whitelist)
				if (strstr(this->EntityTypePtr->ModelName->value, "basebuilding") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodwall") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalwall") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodfloor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalfloor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodfoundation") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalfoundation") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodstairs") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalstairs") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodladderhatch") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalladderhatch") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodramp") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalramp") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "wooddoorframe") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metaldoorframe") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodsingledoor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalsingledoor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "wooddoubledoor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metaldoubledoor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodgaragedoor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalgaragedoor") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodwindow") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalwindow") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodcompoundwall") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalcompoundwall") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodcompoundgate") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalcompoundgate") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodshelter") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalshelter") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodtower") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metaltower") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodwatchtower") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalwatchtower") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodplatform") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalplatform") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodbridge") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalbridge") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "woodfence") != NULL ||
					strstr(this->EntityTypePtr->ModelName->value, "metalfence") != NULL ||
					// Check config names for basebuilding items
					!strcmp("BaseBuilding", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodWall", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalWall", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodFloor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalFloor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodFoundation", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalFoundation", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodStairs", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalStairs", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodLadderHatch", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalLadderHatch", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodRamp", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalRamp", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodDoorFrame", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalDoorFrame", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodSingleDoor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalSingleDoor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodDoubleDoor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalDoubleDoor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodGarageDoor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalGarageDoor", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodWindow", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalWindow", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodCompoundWall", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalCompoundWall", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodCompoundGate", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalCompoundGate", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodShelter", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalShelter", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodTower", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalTower", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodWatchtower", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalWatchtower", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodPlatform", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalPlatform", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodBridge", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalBridge", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("WoodFence", this->EntityTypePtr->ConfigName->value) ||
					!strcmp("MetalFence", this->EntityTypePtr->ConfigName->value)) {
					_entityType = ENTITY_TYPE::BASE;
					return;
				}

				for (auto const whiteListEntry : InventoryItemWhitelist) {
					if (!strcmp(whiteListEntry.c_str(), this->EntityTypePtr->ConfigName->value)) {
						// Special handling for "inventoryItem" - add smart filtering
						if (!strcmp("inventoryItem", whiteListEntry.c_str())) {
							// Only allow inventoryItem if it has a valid CleanName (modded items)
							if (this->EntityTypePtr->CleanName &&
								this->EntityTypePtr->CleanName->value &&
								this->EntityTypePtr->CleanName->length > 0 &&
								this->EntityTypePtr->CleanName->length < 400) {

								std::string cleanName = this->EntityTypePtr->CleanName->value;

								// Filter out known problematic generic items
								if (cleanName != "inventoryItem" &&
									cleanName != "InventoryItem" &&
									cleanName != "Item" &&
									cleanName != "Object" &&
									cleanName != "Entity" &&
									cleanName.find("Trigger") == std::string::npos &&
									cleanName.find("Area") == std::string::npos &&
									cleanName.find("Zone") == std::string::npos) {

									_entityType = ENTITY_TYPE::GROUNDITEM;
									return;
								}
							}
							// Skip this inventoryItem if it doesn't pass smart filtering
							continue;
						}

						// For all other whitelist entries, allow normally
						_entityType = ENTITY_TYPE::GROUNDITEM;
						return;
					}
				}

				// DayZ mod style fallback: Items that don't match any specific category become UNSORTED
				// This ensures all items get categorized instead of remaining NONE
				_entityType = ENTITY_TYPE::UNSORTED;
			}
		}

		ENTITY_TYPE getEntityType() {
			if (!_isEntityTypeChecked) {
				categorizeEntity();
				_isEntityTypeChecked = true;
			}
			return _entityType;
		}

		std::shared_ptr<FutureVisualState> FutureVisualStatePtr;
		std::shared_ptr<EntityType> EntityTypePtr;
		std::shared_ptr<EntityInventory> InventoryPtr;
		uint32_t NetworkID;
		bool isDead;
		float health = 100.0f; // Health value (0-100), placeholder until offset is found
		float blood = 5000.0f; // Blood value (0-5000)
		float shock = 100.0f;  // Shock value (0-100)

		// Health reading functions
		bool ReadHealthValues();
		float ReadHealthType(const std::string& healthType);
		bool IsHealthDataValid();
		

		Entity() {
			FutureVisualStatePtr = std::shared_ptr<FutureVisualState>(new FutureVisualState());
			EntityTypePtr = std::shared_ptr<EntityType>(new EntityType());
			InventoryPtr = std::shared_ptr<EntityInventory>(new EntityInventory());
			this->registerPointer(0x180, EntityTypePtr.get());
			this->registerPointer(0x1C8, FutureVisualStatePtr.get()); // Human::VisualState
			this->registerPointer(0x658, InventoryPtr.get()); // Human::Inventory, DayZPlayer::Inventory, InventoryItem::ItemInventory
			this->registerOffset(0x6E4, &NetworkID, sizeof(uint32_t)); // DayZPlayer::NetworkID
			this->registerOffset(0xE2, &isDead, sizeof(bool));
		}

		

		bool isValid() {
			if (!_isValidChecked) {
				// Validate entity type strings FIRST to prevent null pointer access
				if (!EntityTypePtr || !EntityTypePtr->TypeName || !EntityTypePtr->TypeName->value) {
					_isValid = false;
					_isValidChecked = true;
					return _isValid;
				}

				// Check for blacklisted items using the same logic as debug logging (getBestString)
				auto bestString = this->EntityTypePtr->getBestString();
				if (bestString && bestString->value) {
					std::string entityName = bestString->value;
					if (itemBlacklistSet.find(entityName) != itemBlacklistSet.end()) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
				}

				// Also check TypeName directly as fallback (for compatibility)
				if (this->EntityTypePtr->TypeName && this->EntityTypePtr->TypeName->value) {
					std::string entityTypeName = this->EntityTypePtr->TypeName->value;
					if (itemBlacklistSet.find(entityTypeName) != itemBlacklistSet.end()) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
				}
				
				// Validate position
				if (FutureVisualStatePtr) {
					auto pos = FutureVisualStatePtr->position;
					// Check for invalid coordinates
					if (!std::isfinite(pos.x) || !std::isfinite(pos.y) || !std::isfinite(pos.z)) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
					// Check for extreme values
					if (abs(pos.x) > 50000.0f || abs(pos.y) > 50000.0f || abs(pos.z) > 50000.0f) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
					// Check for zero position
					if (pos.x == 0.0f && pos.y == 0.0f && pos.z == 0.0f) {
						_isValid = false;
						_isValidChecked = true;
						return _isValid;
					}
				}
				
				// Validate string lengths
				_isValid = this->EntityTypePtr->TypeName->length < 400 && 
						  this->EntityTypePtr->TypeName->length > 0 &&
						  this->EntityTypePtr->ConfigName->length < 400 &&
						  this->EntityTypePtr->ModelName->length < 400;
						  
				_isValidChecked = true;
			}
			return _isValid;
		}

		// Optimized hand item retrieval - Performance optimized version
		std::string getHandItemDirect(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				// Step 1: Get inventory pointer (Entity + 0x658)
				QWORD inventoryPtr = 0;
				if (!vmm->readMemory(pid, this->_remoteAddress + 0x658, &inventoryPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (inventoryPtr == 0) return "";

				// Step 2: Get hand item pointer (Inventory + 0x1B0)
				QWORD handItemPtr = 0;
				if (!vmm->readMemory(pid, inventoryPtr + 0x1B0, &handItemPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (handItemPtr == 0) return "";

				// Step 3: Get entity type pointer (HandItem + 0x180)
				QWORD entityTypePtr = 0;
				if (!vmm->readMemory(pid, handItemPtr + 0x180, &entityTypePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (entityTypePtr == 0) return "";

				// Step 4: Get clean name pointer (EntityType + 0x4F0) and read string in one operation
				QWORD cleanNamePtr = 0;
				if (!vmm->readMemory(pid, entityTypePtr + 0x4F0, &cleanNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (cleanNamePtr == 0) return "";

				// Step 5: Read string length and data efficiently
				return readArmaStringDirect(vmm, pid, cleanNamePtr);
			}
			catch (...) {
				return "";
			}
		}

		// Ultra-optimized hand item retrieval - Single chain approach (like DayZ Forum)
		std::string getHandItemDirectOptimized(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				// Single chain of pointer reads - most efficient approach
				QWORD inventoryPtr = 0;
				if (!vmm->readMemory(pid, this->_remoteAddress + 0x658, &inventoryPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE) || inventoryPtr == 0) {
					return "";
				}

				QWORD handItemPtr = 0;
				if (!vmm->readMemory(pid, inventoryPtr + 0x1B0, &handItemPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE) || handItemPtr == 0) {
					return "";
				}

				QWORD entityTypePtr = 0;
				if (!vmm->readMemory(pid, handItemPtr + 0x180, &entityTypePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE) || entityTypePtr == 0) {
					return "";
				}

				QWORD cleanNamePtr = 0;
				if (!vmm->readMemory(pid, entityTypePtr + 0x4F0, &cleanNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE) || cleanNamePtr == 0) {
					return "";
				}

				// Read string data efficiently
				return readArmaStringDirect(vmm, pid, cleanNamePtr);
			}
			catch (...) {
				return "";
			}
		}

		// Cached hand item retrieval - Only reads memory when cache is invalid
		std::string getHandItemCached(DMAMem::VmmManager* vmm, DWORD pid, uint32_t currentFrame) {
			// Check if cache is still valid (cache for 30 frames = ~0.5 seconds at 60fps)
			if (_cachedHandItemValid && (currentFrame - _cachedHandItemFrame) < 30) {
				return _cachedHandItemName;
			}

			// Cache miss - read from memory
			_cachedHandItemName = getHandItemDirectOptimized(vmm, pid);
			_cachedHandItemFrame = currentFrame;
			_cachedHandItemValid = true;

			return _cachedHandItemName;
		}

		// Force refresh hand item cache
		void refreshHandItemCache() {
			_cachedHandItemValid = false;
			_cachedHandItemName = "";
		}

		// Get hand item CleanName (display name) - DayZ2 style
		std::string getHandItemCleanName(DMAMem::VmmManager* vmm, DWORD pid) {
			return getHandItemDirect(vmm, pid); // Same as above, for clarity
		}

		// Get hand item ConfigName (category name) - DayZ2 style
		std::string getHandItemConfigName(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				// Step 1: Get inventory pointer (Entity + 0x658)
				QWORD inventoryPtr = 0;
				if (!vmm->readMemory(pid, this->_remoteAddress + 0x658, &inventoryPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (inventoryPtr == 0) return "";

				// Step 2: Get hand item pointer (Inventory + 0x1B0)
				QWORD handItemPtr = 0;
				if (!vmm->readMemory(pid, inventoryPtr + 0x1B0, &handItemPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (handItemPtr == 0) return "";

				// Step 3: Get entity type pointer (HandItem + 0x180)
				QWORD entityTypePtr = 0;
				if (!vmm->readMemory(pid, handItemPtr + 0x180, &entityTypePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (entityTypePtr == 0) return "";

				// Step 4: Get config name pointer (EntityType + 0xA8) - Category name
				QWORD configNamePtr = 0;
				if (!vmm->readMemory(pid, entityTypePtr + 0xA8, &configNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (configNamePtr == 0) return "";

				return readArmaStringDirect(vmm, pid, configNamePtr);
			}
			catch (...) {
				return "";
			}
		}

		// Get hand item ModelName (model path) - DayZ2 style
		std::string getHandItemModelName(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				// Step 1: Get inventory pointer (Entity + 0x658)
				QWORD inventoryPtr = 0;
				if (!vmm->readMemory(pid, this->_remoteAddress + 0x658, &inventoryPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (inventoryPtr == 0) return "";

				// Step 2: Get hand item pointer (Inventory + 0x1B0)
				QWORD handItemPtr = 0;
				if (!vmm->readMemory(pid, inventoryPtr + 0x1B0, &handItemPtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (handItemPtr == 0) return "";

				// Step 3: Get entity type pointer (HandItem + 0x180)
				QWORD entityTypePtr = 0;
				if (!vmm->readMemory(pid, handItemPtr + 0x180, &entityTypePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (entityTypePtr == 0) return "";

				// Step 4: Get model name pointer (EntityType + 0x88) - Model path
				QWORD modelNamePtr = 0;
				if (!vmm->readMemory(pid, entityTypePtr + 0x88, &modelNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (modelNamePtr == 0) return "";

				return readArmaStringDirect(vmm, pid, modelNamePtr);
			}
			catch (...) {
				return "";
			}
		}

	private:

	public:

		// Get entity CleanName (display name) - DayZ2 style
		std::string getEntityCleanName(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				if (!this->EntityTypePtr) return "";

				QWORD cleanNamePtr = 0;
				if (!vmm->readMemory(pid, this->EntityTypePtr->_remoteAddress + 0x4F0, &cleanNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (cleanNamePtr == 0) return "";

				return readArmaStringDirect(vmm, pid, cleanNamePtr);
			}
			catch (...) {
				return "";
			}
		}

		// Get entity ConfigName (category name) - DayZ2 style
		std::string getEntityConfigName(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				if (!this->EntityTypePtr) return "";

				QWORD configNamePtr = 0;
				if (!vmm->readMemory(pid, this->EntityTypePtr->_remoteAddress + 0xA8, &configNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (configNamePtr == 0) return "";

				return readArmaStringDirect(vmm, pid, configNamePtr);
			}
			catch (...) {
				return "";
			}
		}

		// Get entity ModelName (model path) - DayZ2 style
		std::string getEntityModelName(DMAMem::VmmManager* vmm, DWORD pid) {
			try {
				if (!this->EntityTypePtr) return "";

				QWORD modelNamePtr = 0;
				if (!vmm->readMemory(pid, this->EntityTypePtr->_remoteAddress + 0x88, &modelNamePtr, sizeof(QWORD), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}
				if (modelNamePtr == 0) return "";

				return readArmaStringDirect(vmm, pid, modelNamePtr);
			}
			catch (...) {
				return "";
			}
		}

		// Helper function to read ArmaString directly
		std::string readArmaStringDirect(DMAMem::VmmManager* vmm, DWORD pid, QWORD stringPtr) {
			try {
				// Read string length
				int32_t length = 0;
				if (!vmm->readMemory(pid, stringPtr + 0x8, &length, sizeof(int32_t), VMMDLL_FLAG_NOCACHE)) {
					return "";
				}

				if (length <= 0 || length > 200) return ""; // Sanity check

				// Read string data
				std::vector<char> buffer(length + 1, 0);
				if (!vmm->readMemory(pid, stringPtr + 0x10, buffer.data(), length, VMMDLL_FLAG_NOCACHE)) {
					return "";
				}

				return std::string(buffer.data());
			}
			catch (...) {
				return "";
			}
		}

		// Test function to show all name types (for debugging)
		std::vector<std::string> getAllHandItemNames(DMAMem::VmmManager* vmm, DWORD pid) {
			std::vector<std::string> names;
			names.push_back("CleanName: " + getHandItemCleanName(vmm, pid));
			names.push_back("ConfigName: " + getHandItemConfigName(vmm, pid));
			names.push_back("ModelName: " + getHandItemModelName(vmm, pid));
			return names;
		}

		bool isPlayer() {
			return getEntityType() == PLAYER;
		}

		bool isZombie() {
			return getEntityType() == ZOMBIE;
		}

		bool isCar() {
			return getEntityType() == CAR;
		}

		bool isBoat() {
			return getEntityType() == BOAT;
		}

		bool isAnimal() {
			return getEntityType() == ANIMAL;
		}

		bool isRare() {
			return getEntityType() == RARE;
		}

		bool isBackpack() {
			return getEntityType() == BACKPACK;
		}

		bool isClothing() {
			return getEntityType() == CLOTHING;
		}

		bool isWeapon() {
			return getEntityType() == WEAPON;
		}

		bool isProxyMagazines() {
			return getEntityType() == PROXYMAGAZINES;
		}

		bool isFood() {
			return getEntityType() == FOOD;
		}

		bool isAmmo() {
			return getEntityType() == AMMO;
		}

		bool isGroundItem() {
			return getEntityType() == GROUNDITEM;
		}

		bool isOptic() {
			return getEntityType() == OPTICS;
		}

		bool isBase() {
			return getEntityType() == BASE;
		}

		bool isMelee() {
			return getEntityType() == MELEE;
		}

		bool isExplosives() {
			return getEntityType() == EXPLOSIVES;
		}

		bool isContainer() {
			return getEntityType() == CONTAINER;
		}

		bool isCooking() {
			return getEntityType() == COOKING;
		}

		bool isCamping() {
			return getEntityType() == CAMPING;
		}

		bool isStash() {
			return getEntityType() == STASH;
		}

		bool isUnsorted() {
			return getEntityType() == UNSORTED;
		}


		std::shared_ptr<ScoreboardIdentity> getPlayerIdentity(DayZ::Scoreboard* scoreboard) {
			for (const auto ident : scoreboard->resolvedIdentities) {
				if (ident->NetworkID == this->NetworkID)
					return ident;
			}
			return NULL;
		}

	};

}