#pragma once
#include "DMARender/includes.h"
#include "../MemoryUpdater/MemoryUpdater.h"
#include <fstream>
namespace DayZ {
	class RadarAdapter : public DMARender::IRadar {
		std::shared_ptr<DayZ::MemoryUpdater> memUpdater;
		std::shared_ptr<DMARender::RenderBridge> renderBridge;
		std::set<std::string> favoriteSteamIDs;

		// Performance optimization structures
		std::unordered_map<uint32_t, std::chrono::steady_clock::time_point> entityCache;
		const std::chrono::milliseconds CACHE_DURATION{100}; // Increased from 50ms to 100ms for better caching
		const float MAX_RENDER_DISTANCE = 3000.0f; // Reduced from 5000 to 3000 for better performance
		const float MIN_RENDER_DISTANCE = 2.0f; // Reduced from 3.1 to 2.0 for closer entity visibility
		std::vector<std::shared_ptr<DayZ::Entity>> batchEntities;

		void loadFavoriteSteamIDs(const std::string& filePath);
		void saveFavoriteSteamIDs(const std::string& filePath);
		void addFavoriteSteamID(const std::string& steamID);
		void removeFavoriteSteamID(const std::string& steamID);
		bool isFavoriteSteamID(const std::string& steamID) const;
		ImFont* radarFont;
		ImFont* tableFont;

		// New performance optimization methods
		bool isEntityCached(uint32_t entityId);
		void updateEntityCache(uint32_t entityId);
		void clearExpiredCache();
		void batchProcessEntities(const std::vector<std::shared_ptr<DayZ::Entity>>& entities);
		bool shouldRenderEntity(const DMARender::Vector3& position, const DMARender::Vector3& cameraPos, float maxDistance);

		void drawLoot(DayZ::Camera* camera, DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform, const std::vector<std::shared_ptr<DayZ::Entity>>& entities);
		void drawAliveEntities(DayZ::Camera* camera, DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform, const std::vector<std::shared_ptr<DayZ::Entity>>& entities, Scoreboard* scoreboard);
		void drawPlayerList(DayZ::Camera* camera, Scoreboard* scoreboard);
		void drawServerPlayerList(std::shared_ptr<DayZ::Scoreboard> scoreboard);

	public:
		RadarAdapter(std::shared_ptr<DayZ::MemoryUpdater> memUpdater, std::shared_ptr<DMARender::RenderBridge> renderBridge);
		virtual void DrawOverlay(DMARender::IGameMap* curMap, const DMARender::MapTransform& mTransform) override;
		virtual void createFonts() override;
	};
}